<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartReport - Effortless Assessment Management</title>

    <!-- Tailwind CSS (Local Build) -->
    <link href="../assets/css/output.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom Styles -->
    <style>
        /* Custom animations for login page */
        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        .animate-slide-in-left {
            animation: slideInLeft 0.8s ease-out;
        }

        .animate-slide-in-right {
            animation: slideInRight 0.8s ease-out;
        }

        .animate-slide-up {
            animation: slideUp 0.6s ease-out;
        }

        .animate-fade-in {
            animation: fadeIn 0.8s ease-out;
        }

        /* Custom shadow styles */
        .shadow-soft {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .shadow-medium {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .shadow-large {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .shadow-glow {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.15);
        }

        /* Gradient backgrounds */
        .bg-gradient-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .bg-gradient-secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .bg-gradient-success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        /* Input focus effects */
        .input-focus-effect:focus {
            transform: translateY(-1px);
        }

        /* Button hover effects */
        .btn-hover-lift:hover {
            transform: translateY(-2px);
        }
    </style>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/images/logo.png">

    <!--  Component Styles -->
    <style>
        /*  component styles for new features */
        .search-filter {
            padding: 0.5rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 500;
            border-radius: 0.5rem;
            transition: all 0.2s;
            border: 1px solid #E5E7EB;
            color: #6B7280;
            background: white;
            cursor: pointer;
        }

        .search-filter:hover {
            background: #F9FAFB;
        }

        .search-filter.active {
            background: #3B82F6;
            color: white;
            border-color: #3B82F6;
        }

        .export-type-btn, .export-format-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            font-size: 0.875rem;
            font-weight: 500;
            border-radius: 0.75rem;
            border: 2px solid #E5E7EB;
            transition: all 0.2s;
            color: #6B7280;
            background: white;
            cursor: pointer;
        }

        .export-type-btn:hover, .export-format-btn:hover {
            border-color: #93C5FD;
            background: #EFF6FF;
        }

        .export-type-btn.active, .export-format-btn.active {
            background: #3B82F6;
            color: white;
            border-color: #3B82F6;
        }

        .form-input, .form-select, .form-textarea {
            display: block;
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid #D1D5DB;
            border-radius: 0.75rem;
            transition: all 0.2s;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            box-shadow: 0 0 0 2px #3B82F6;
            border-color: #3B82F6;
        }

        .form-checkbox {
            width: 1rem;
            height: 1rem;
            color: #2563EB;
            border-color: #D1D5DB;
            border-radius: 0.25rem;
        }

        .form-checkbox:focus {
            box-shadow: 0 0 0 2px #3B82F6;
        }

        .btn-primary {
            background: #2563EB;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.75rem;
            font-weight: 500;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            border: none;
            cursor: pointer;
        }

        .btn-primary:hover {
            background: #1D4ED8;
        }

        .btn-secondary {
            background: #F3F4F6;
            color: #374151;
            padding: 0.75rem 1.5rem;
            border-radius: 0.75rem;
            font-weight: 500;
            transition: all 0.2s;
            border: none;
            cursor: pointer;
        }

        .btn-secondary:hover {
            background: #E5E7EB;
        }

        .kbd {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            font-weight: 600;
            color: #6B7280;
            background: #F3F4F6;
            border: 1px solid #D1D5DB;
            border-radius: 0.25rem;
        }

        /*  Sidebar Navigation Styles */
        .sidebar-menu-section {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .sidebar-menu-section.collapsed {
            max-height: 0;
            opacity: 0;
            margin-top: 0;
            margin-bottom: 0;
        }

        .sidebar-menu-section.expanded {
            max-height: 500px;
            opacity: 1;
        }

        .sidebar-menu-button {
            position: relative;
            overflow: hidden;
        }

        .sidebar-menu-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .sidebar-menu-button:hover::before {
            left: 100%;
        }

        .sidebar-chevron {
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .sidebar-chevron.rotated {
            transform: rotate(180deg);
        }

        /* Active menu item indicator */
        .menu-item-active {
            position: relative;
        }

        .menu-item-active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 60%;
            background: linear-gradient(to bottom, #3B82F6, #1D4ED8);
            border-radius: 0 2px 2px 0;
        }

        /* Submenu animation */
        .submenu-enter {
            animation: submenuSlideIn 0.3s ease-out forwards;
        }

        .submenu-exit {
            animation: submenuSlideOut 0.3s ease-out forwards;
        }

        @keyframes submenuSlideIn {
            from {
                max-height: 0;
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                max-height: 300px;
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes submenuSlideOut {
            from {
                max-height: 300px;
                opacity: 1;
                transform: translateY(0);
            }
            to {
                max-height: 0;
                opacity: 0;
                transform: translateY(-10px);
            }
        }

        /* Hover effects for menu items */
        .menu-item:hover {
            transform: translateX(2px);
        }

        .submenu-item:hover {
            transform: translateX(4px);
        }

        /* Focus styles for accessibility */
        .sidebar-menu-button:focus,
        .menu-item:focus,
        .submenu-item:focus {
            outline: 2px solid #3B82F6;
            outline-offset: 2px;
        }

        /* Student Action Dropdown Styles */
        .student-action-dropdown {
            position: relative;
            display: inline-block;
        }

        .student-action-dropdown .dropdown-menu {
            position: absolute;
            right: 0;
            top: 100%;
            z-index: 1000;
            min-width: 12rem;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            padding: 0.25rem 0;
            margin-top: 0.5rem;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            pointer-events: none;
        }

        .student-action-dropdown .dropdown-menu.show {
            opacity: 1;
            transform: translateY(0);
            pointer-events: auto;
        }

        .student-action-dropdown .dropdown-item {
            display: flex;
            align-items: center;
            width: 100%;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            color: #374151;
            text-align: left;
            background: transparent;
            border: none;
            cursor: pointer;
            transition: background-color 0.15s ease-in-out;
        }

        .student-action-dropdown .dropdown-item:hover {
            background-color: #f9fafb;
        }

        .student-action-dropdown .dropdown-item:focus {
            outline: none;
            background-color: #f3f4f6;
        }

        .student-action-dropdown .dropdown-item i {
            width: 1rem;
            margin-right: 0.75rem;
            text-align: center;
        }

        /* Dropdown button hover effect */
        .student-action-dropdown .dropdown-toggle:hover {
            background-color: #f3f4f6;
        }

        .student-action-dropdown .dropdown-toggle:focus {
            outline: 2px solid #3b82f6;
            outline-offset: 2px;
        }

        /* Collapsible Sidebar Styles - Optimized for Desktop */
        .sidebar {
            transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            width: 280px; /* Reduced from 320px for better desktop space usage */
            overflow-x: hidden;
            min-width: 0;
            max-width: 280px;
        }

        .sidebar.collapsed {
            width: 70px; /* Reduced from 80px for cleaner look */
            overflow-x: hidden;
        }


        .sidebar-text {
            transition: opacity 0.3s ease, transform 0.3s ease;
            opacity: 1;
            transform: translateX(0);
            word-wrap: break-word;
            overflow-wrap: break-word;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }

        .sidebar.collapsed .sidebar-text {
            opacity: 0;
            transform: translateX(-10px);
            pointer-events: none;
        }

        .sidebar-brand-text {
            transition: opacity 0.3s ease, transform 0.3s ease;
            opacity: 1;
            transform: translateX(0);
            overflow: hidden;
            min-width: 0;
            flex-shrink: 1;
        }

        .sidebar.collapsed .sidebar-brand-text {
            opacity: 0;
            transform: translateX(-10px);
        }

        .sidebar-toggle-btn {
            transition: transform 0.3s ease;
        }

        .sidebar.collapsed .sidebar-toggle-btn {
            transform: rotate(180deg);
        }

        /* Menu item styles for collapsed state */
        .sidebar.collapsed .menu-item {
            justify-content: center;
            padding: 0.75rem;
        }

        .sidebar.collapsed .submenu-item {
            display: none;
        }

        .sidebar.collapsed .sidebar-menu-section {
            display: none;
        }

        /* Tooltip for collapsed sidebar */
        .sidebar-tooltip {
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            background: #1F2937;
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
            z-index: 1000;
            margin-left: 0.5rem;
            pointer-events: none;
        }

        .sidebar-tooltip::before {
            content: '';
            position: absolute;
            right: 100%;
            top: 50%;
            transform: translateY(-50%);
            border: 5px solid transparent;
            border-right-color: #1F2937;
        }

        .sidebar.collapsed .menu-item:hover .sidebar-tooltip {
            opacity: 1;
            visibility: visible;
        }

        /* Active menu item highlighting */
        .menu-item.active {
            background: linear-gradient(135deg, #3B82F6, #1D4ED8);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .menu-item.active .menu-icon {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .submenu-item.active {
            background: linear-gradient(135deg, #3B82F6, #1D4ED8);
            color: white;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
        }

        .submenu-item.active .submenu-dot {
            background: white;
        }

        /* Main content adjustment */
        .main-content {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-left: 0;
        }

        .main-content.sidebar-collapsed {
            margin-left: 0;
        }







        /*  animations for sidebar toggle */
        @keyframes sidebarExpand {
            from {
                width: 80px;
            }
            to {
                width: 320px;
            }
        }

        @keyframes sidebarCollapse {
            from {
                width: 320px;
            }
            to {
                width: 80px;
            }
        }

        /* Active menu item glow effect */
        .menu-item.active::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(29, 78, 216, 0.1));
            border-radius: 0.75rem;
            z-index: -1;
        }

        /* Submenu active item glow effect */
        .submenu-item.active::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(29, 78, 216, 0.1));
            border-radius: 0.5rem;
            z-index: -1;
        }

        /* Sidebar header logo animation */
        .sidebar.collapsed .sidebar-brand-text {
            transform: scale(0) translateX(-20px);
        }

        /* Menu item icon scaling in collapsed state */
        .sidebar.collapsed .menu-icon {
            transform: scale(1.1);
        }

        /* Smooth transitions for all sidebar elements */
        .sidebar * {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Prevent text selection during animations */
        .sidebar.collapsed * {
            user-select: none;
        }

        /* Enhanced Modal Backdrop Styles */
        .modal-backdrop {
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            background: rgba(0, 0, 0, 0.5);
            animation: backdropFadeIn 0.3s ease-out forwards;
        }

        .modal-backdrop.closing {
            animation: backdropFadeOut 0.2s ease-out forwards;
        }

        @keyframes backdropFadeIn {
            from {
                opacity: 0;
                backdrop-filter: blur(0px);
                -webkit-backdrop-filter: blur(0px);
            }
            to {
                opacity: 1;
                backdrop-filter: blur(8px);
                -webkit-backdrop-filter: blur(8px);
            }
        }

        @keyframes backdropFadeOut {
            from {
                opacity: 1;
                backdrop-filter: blur(8px);
                -webkit-backdrop-filter: blur(8px);
            }
            to {
                opacity: 0;
                backdrop-filter: blur(0px);
                -webkit-backdrop-filter: blur(0px);
            }
        }

        /* Enhanced Modal Content Animation */
        .animate-modal-in {
            animation: modalSlideIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
        }

        .animate-modal-out {
            animation: modalSlideOut 0.2s ease-out forwards;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        @keyframes modalSlideOut {
            from {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
            to {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
        }

        /* Prevent horizontal scrolling */
        body, html {
            overflow-x: hidden;
        }

        /* Ensure main content doesn't overflow */
        .main-content {
            overflow-x: hidden;
            max-width: 100vw;
        }

        /* Sidebar content overflow prevention */
        .sidebar .flex-1 {
            overflow-x: hidden;
            min-width: 0;
        }

        .sidebar nav {
            overflow-x: hidden;
            min-width: 0;
        }

        .sidebar ul {
            overflow-x: hidden;
            min-width: 0;
        }

        .sidebar li {
            overflow-x: hidden;
            min-width: 0;
        }

        /* Menu item text handling */
        .sidebar .menu-item,
        .sidebar .submenu-item {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            min-width: 0;
        }

        /* Sidebar toggle button tooltip */
        .sidebar-toggle-tooltip {
            position: absolute;
            right: -120px;
            top: 50%;
            transform: translateY(-50%);
            background: #1F2937;
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
            z-index: 1000;
            pointer-events: none;
        }

        .sidebar-toggle-tooltip::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 50%;
            transform: translateY(-50%);
            border: 5px solid transparent;
            border-right-color: #1F2937;
        }

        .sidebar-toggle-btn:hover .sidebar-toggle-tooltip {
            opacity: 1;
            visibility: visible;
        }

        /* Enhanced toggle button animation */
        .sidebar-toggle-btn {
            position: relative;
        }

        .sidebar-toggle-btn:hover {
            transform: scale(1.05);
        }

        .sidebar.collapsed .sidebar-toggle-btn i {
            transform: rotate(180deg);
        }





        /* Desktop-optimized responsive design */

        /* Large Desktop (1400px+) */
        @media (min-width: 1400px) {
            .sidebar {
                width: 300px;
            }
            .sidebar.collapsed {
                width: 75px;
            }
            .content-body {
                padding: 2rem;
            }
        }

        /* Standard Desktop (1200px - 1399px) */
        @media (min-width: 1200px) and (max-width: 1399px) {
            .sidebar {
                width: 280px;
            }
            .sidebar.collapsed {
                width: 70px;
            }
            .content-body {
                padding: 1.5rem;
            }
        }

        /* Small Desktop (1024px - 1199px) */
        @media (min-width: 1024px) and (max-width: 1199px) {
            .sidebar {
                width: 260px;
            }
            .sidebar.collapsed {
                width: 65px;
            }
            .content-body {
                padding: 1.25rem;
            }
        }

        /* Minimum desktop size - no mobile support */
        @media (max-width: 1023px) {
            .sidebar {
                width: 260px;
            }
            .sidebar.collapsed {
                width: 65px;
            }
            .content-body {
                padding: 1rem;
            }
        }

        /* Desktop layout optimization */
        .desktop-layout {
            display: flex;
            height: 100vh;
            overflow: hidden;
            background: #f9fafb;
        }

        .content-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            min-width: 0;
        }

        .content-body {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            background: #f9fafb;
            transition: padding 0.3s ease;
        }

        /* Smooth transitions for all layout changes */
        .sidebar, .main-content, .content-wrapper {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Modern Layout Styles */
        .nav-section-items.collapsed {
            max-height: 0;
            opacity: 0;
        }

        .nav-section-items.expanded {
            max-height: 500px;
            opacity: 1;
        }

        /* Sidebar Collapsed State Styles */
        #sidebar.sidebar-collapsed {
            overflow-x: hidden;
        }

        #sidebar.sidebar-collapsed .nav-text {
            opacity: 0;
            width: 0;
            overflow: hidden;
            white-space: nowrap;
            transition: all 0.3s ease;
        }

        #sidebar.sidebar-collapsed .nav-item,
        #sidebar.sidebar-collapsed .nav-section {
            position: relative;
        }

        #sidebar.sidebar-collapsed .nav-item:hover::after,
        #sidebar.sidebar-collapsed .nav-section:hover::after {
            content: attr(title);
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            background: #1f2937;
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            white-space: nowrap;
            z-index: 1000;
            margin-left: 0.5rem;
            opacity: 1;
            visibility: visible;
            transition: all 0.2s ease;
            pointer-events: none;
        }

        #sidebar.sidebar-collapsed .nav-item:hover::before,
        #sidebar.sidebar-collapsed .nav-section:hover::before {
            content: '';
            position: absolute;
            left: calc(100% + 0.25rem);
            top: 50%;
            transform: translateY(-50%);
            border: 5px solid transparent;
            border-right-color: #1f2937;
            z-index: 1001;
        }

        /* Ensure icons are centered when collapsed */
        #sidebar.sidebar-collapsed .nav-item button,
        #sidebar.sidebar-collapsed .nav-section button {
            justify-content: center;
            padding-left: 0.5rem;
            padding-right: 0.5rem;
        }

        /* Hide chevron icons when collapsed */
        #sidebar.sidebar-collapsed .fa-chevron-down {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        /* Smooth hover effects */
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        /* Loading states */
        .loading-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
    </style>
</head>
<body>
    <!--  Loading Screen -->
    <div id="loading-screen" class="fixed inset-0 bg-gradient-to-br from-primary-600 via-secondary-600 to-accent-600 flex items-center justify-center z-50 overflow-hidden">
        <!-- Animated Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-shimmer" style="background-size: 200% 100%;"></div>
            <div class="absolute top-0 left-0 w-full h-full">
                <div class="absolute top-1/4 left-1/4 w-32 h-32 bg-white/5 rounded-full animate-float" style="animation-delay: 0s;"></div>
                <div class="absolute top-3/4 right-1/4 w-24 h-24 bg-white/5 rounded-full animate-float" style="animation-delay: 1s;"></div>
                <div class="absolute bottom-1/4 left-1/3 w-16 h-16 bg-white/5 rounded-full animate-float" style="animation-delay: 2s;"></div>
            </div>
        </div>

        <div class="relative z-10 text-center text-white animate-fade-in max-w-md mx-auto px-6">
            <!--  Logo Container -->
            <div class="mb-8 animate-bounce-in">
                <div class="relative w-32 h-32 mx-auto mb-6">
                    <!-- Outer Ring -->
                    <div class="absolute inset-0 bg-gradient-to-r from-white/20 to-white/10 rounded-full animate-spin-slow"></div>
                    <!-- Inner Circle -->
                    <div class="absolute inset-2 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center border-2 border-white/30 shadow-glow">
                        <img src="../assets/images/logo.png" alt="SR Logo" class="w-20 h-20 object-contain hidden animate-glow" id="logo-image">
                        <div class="text-4xl font-bold text-white animate-glow" id="logo-placeholder">SR</div>
                    </div>
                    <!-- Pulse Ring -->
                    <div class="absolute inset-0 border-2 border-white/20 rounded-full animate-ping"></div>
                </div>
            </div>

            <!--  Loading Text -->
            <div class="mb-8 animate-slide-up">
                <h1 class="text-3xl font-bold mb-3 tracking-wide bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                    Report Card System
                </h1>
                <p id="loading-message" class="text-xl opacity-90 font-medium mb-2">Initializing system...</p>
            </div>

            <!-- Advanced Loading Indicator -->
            <div class="mb-8">
                <!-- Progress Bar -->
                <div class="w-64 h-2 bg-white/20 rounded-full mx-auto mb-4 overflow-hidden">
                    <div class="h-full bg-gradient-to-r from-white/60 to-white/80 rounded-full animate-shimmer" style="width: 0%; animation-duration: 3s; animation-iteration-count: infinite;"></div>
                </div>

                <!-- Spinner -->
                <div class="flex justify-center">
                    <div class="relative">
                        <div class="w-16 h-16 border-4 border-white/20 border-t-white/80 rounded-full animate-spin"></div>
                        <div class="absolute inset-2 border-2 border-transparent border-r-white/40 rounded-full animate-spin" style="animation-direction: reverse; animation-duration: 2s;"></div>
                        <div class="absolute inset-4 w-4 h-4 bg-white/60 rounded-full animate-pulse"></div>
                    </div>
                </div>
            </div>

            <!--  Progress Dots -->
            <div class="flex justify-center space-x-3">
                <div class="flex space-x-1">
                    <div class="w-3 h-3 bg-white/60 rounded-full animate-pulse" style="animation-delay: 0s;"></div>
                    <div class="w-3 h-3 bg-white/60 rounded-full animate-pulse" style="animation-delay: 0.3s;"></div>
                    <div class="w-3 h-3 bg-white/60 rounded-full animate-pulse" style="animation-delay: 0.6s;"></div>
                </div>
            </div>

            <!-- Loading Status -->
            <div class="mt-6 text-sm opacity-70">
                <div id="loading-status" class="animate-fade-in">Preparing your workspace...</div>
            </div>
        </div>
    </div>

    <!--  Login Screen -->
    <div id="login-screen" class="fixed inset-0 flex z-40" style="display: none;">
        <!-- Left Panel -  Branding -->
        <div class="flex-1 bg-gradient-to-br from-primary-600 via-secondary-600 to-accent-600 flex flex-col items-center justify-center p-8 text-white relative overflow-hidden animate-slide-in-left">
            <!--  Background Pattern -->
            <div class="absolute inset-0 opacity-10">
                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-shimmer" style="background-size: 200% 100%;"></div>
                <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 100 100&quot;><defs><pattern id=&quot;grid&quot; width=&quot;10&quot; height=&quot;10&quot; patternUnits=&quot;userSpaceOnUse&quot;><path d=&quot;M 10 0 L 0 0 0 10&quot; fill=&quot;none&quot; stroke=&quot;rgba(255,255,255,0.1)&quot; stroke-width=&quot;0.5&quot;/></pattern></defs><rect width=&quot;100&quot; height=&quot;100&quot; fill=&quot;url(%23grid)&quot;/></svg>');"></div>
                <!-- Floating Elements -->
                <div class="absolute top-1/4 left-1/4 w-32 h-32 bg-white/5 rounded-full animate-float" style="animation-delay: 0s;"></div>
                <div class="absolute top-3/4 right-1/4 w-24 h-24 bg-white/5 rounded-full animate-float" style="animation-delay: 1s;"></div>
                <div class="absolute bottom-1/4 left-1/3 w-16 h-16 bg-white/5 rounded-full animate-float" style="animation-delay: 2s;"></div>
            </div>

            <div class="relative z-10 text-center max-w-lg animate-fade-in">
                <!--  Logo -->
                <div class="mb-8">
                    <div class="relative w-32 h-32 mx-auto mb-6">
                        <!-- Outer Ring -->
                        <div class="absolute inset-0 bg-gradient-to-r from-white/20 to-white/10 rounded-full animate-spin-slow"></div>
                        <!-- Inner Circle -->
                        <div class="absolute inset-2 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center border-2 border-white/30 shadow-glow">
                            <img src="../assets/images/logo.png" alt="SR Logo" class="w-24 h-24 object-contain hidden animate-glow" onload="this.classList.remove('hidden'); this.nextElementSibling && this.nextElementSibling.classList.add('hidden');" onerror="this.classList.add('hidden'); this.nextElementSibling && this.nextElementSibling.classList.remove('hidden');">
                        </div>
                        <!-- Pulse Ring -->
                        <div class="absolute inset-0 border-2 border-white/20 rounded-full animate-ping"></div>
                    </div>
                </div>

                <!--  Title and Subtitle -->
                <div class="mt-12">
                    <h1 class="text-3xl font-bold mb-4 leading-tight bg-gradient-to-r from-white to-white/90 bg-clip-text text-transparent">
                        SmartReport
                    </h1>
                    <p class="text-xl mb-8 opacity-90 font-light">"Effortless Assessment Management"</p>
                </div>

                <!--  Features List -->
                <div class="grid grid-cols-2 gap-4 mb-8">
                    <div class="flex items-center space-x-3 animate-slide-up" style="animation-delay: 0.1s;">
                        <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                            <i class="fas fa-user-graduate text-lg"></i>
                        </div>
                        <span class="font-medium text-left">Student Records</span>
                    </div>

                    <div class="flex items-center space-x-3 animate-slide-up" style="animation-delay: 0.2s;">
                        <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                            <i class="fas fa-clipboard-check text-lg"></i>
                        </div>
                        <span class="font-medium text-left">Assessments</span>
                    </div>

                    <div class="flex items-center space-x-3 animate-slide-up" style="animation-delay: 0.3s;">
                        <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                            <i class="fas fa-file-alt text-lg"></i>
                        </div>
                        <span class="font-medium text-left">Report Cards</span>
                    </div>

                    <div class="flex items-center space-x-3 animate-slide-up" style="animation-delay: 0.4s;">
                        <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                            <i class="fas fa-shield-alt text-lg"></i>
                        </div>
                        <span class="font-medium text-left">Secure Access</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Panel -  Login Form -->
        <div class="flex-1 bg-white flex items-center justify-center p-8 animate-slide-in-right">
            <div class="w-full max-w-sm">
                <!--  Login Header -->
                <div class="text-center mb-8 animate-fade-in">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Welcome Back</h1>
                    <p class="text-gray-600">Sign in to access the system</p>
                </div>

                <!--  Login Form -->
                <div class="w-full">
                    <!-- Error Display Area -->
                    <div id="login-error" class="hidden mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-circle text-red-400"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-red-800" id="login-error-message"></p>
                            </div>
                            <div class="ml-auto pl-3">
                                <button type="button" onclick="hideLoginError()" class="inline-flex text-red-400 hover:text-red-600 focus:outline-none">
                                    <i class="fas fa-times text-sm"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Success Display Area -->
                    <div id="login-success" class="hidden mb-6 p-4 bg-green-50 border border-green-200 rounded-xl">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-check-circle text-green-400"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-green-800" id="login-success-message"></p>
                            </div>
                        </div>
                    </div>

                    <form id="login-form" class="space-y-6">
                        <!-- Username Field -->
                        <div class="animate-slide-up" style="animation-delay: 0.1s;">
                            <label for="username" class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-user text-gray-400"></i>
                                </div>
                                <input type="text" id="username" name="username" required
                                       class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-200 bg-white text-gray-900 placeholder-gray-500"
                                       placeholder="Enter your username"
                                       autocomplete="username">
                            </div>
                        </div>

                        <!-- Password Field -->
                        <div class="animate-slide-up" style="animation-delay: 0.2s;">
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-lock text-gray-400"></i>
                                </div>
                                <input type="password" id="password" name="password" required
                                       class="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-200 bg-white text-gray-900 placeholder-gray-500"
                                       placeholder="Enter your password"
                                       autocomplete="current-password">
                                <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-primary-600 transition-colors duration-200" onclick="togglePassword()">
                                    <i class="fas fa-eye" id="password-toggle-icon"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Remember Me & Forgot Password -->
                        <div class="flex items-center justify-between animate-slide-up" style="animation-delay: 0.25s;">
                            <label class="flex items-center cursor-pointer">
                                <input type="checkbox" id="remember-me" name="remember_me" class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 focus:ring-2 transition-colors">
                                <span class="ml-2 text-sm text-gray-600 select-none">Remember me</span>
                            </label>
                            <a href="#" onclick="showForgotPassword()" class="text-sm text-primary-600 hover:text-primary-800 font-medium transition-colors duration-300 hover:underline">
                                Forgot Password?
                            </a>
                        </div>

                        <!--  Login Button -->
                        <div class="animate-slide-up" style="animation-delay: 0.3s;">
                            <button type="submit" id="login-submit-btn" class="w-full bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                                <span id="login-btn-text">Sign In</span>
                            </button>
                        </div>

                        <!-- Login Help -->
                        <div class="text-center animate-slide-up" style="animation-delay: 0.4s;">
                            <button type="button" onclick="showLoginHelp()" class="text-sm text-gray-500 hover:text-primary-600 transition-colors duration-200">
                                Need help? Contact Administrator
                            </button>
                        </div>
                    </form>
                </div>


            </div>
        </div>
    </div>

    <!--  Main Application -->
    <div id="main-app" style="display: none;">
        <!-- Layout will be injected here by Layout.init() -->
    </div>






    <!--  Modal Container -->
    <div id="modal-container" class="fixed inset-0 z-50 hidden">
        <!-- Modal backdrop with blur effect -->
        <div class="absolute inset-0 bg-black/50 backdrop-blur-sm"></div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 z-40 bg-white/80 backdrop-blur-sm hidden">
        <div class="flex items-center justify-center h-full">
            <div class="text-center">
                <div class="w-16 h-16 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin mx-auto mb-4"></div>
                <p class="text-gray-600 font-medium">Loading...</p>
            </div>
        </div>
    </div>


    <!-- JavaScript Files -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>


    <!-- Environment Configuration (loaded first) -->
    <script src="../assets/js/config/environment.js"></script>

    <!-- API Services (loaded second) -->
    <script src="../assets/js/services/api.js"></script>

    <!-- Academic Context Manager (loaded after API services) -->
    <script src="../assets/js/services/academic-context.js"></script>

    <!-- Layout System (loaded third) -->
    <script src="../assets/js/layout.js"></script>
    <script src="../assets/js/navigation.js"></script>
    <script src="../assets/js/dashboard.js"></script>
    <script src="../assets/js/page-router.js"></script>

    <!-- Core Application Scripts -->
    <script src="../assets/js/design-system.js"></script>
    <script src="../assets/js/utils.js"></script>
    <script src="../assets/js/ui-helpers.js"></script>
    <script src="../assets/js/component-lifecycle-manager.js"></script>


    <!-- Authentication -->
    <script src="../assets/js/auth.js"></script>

    <!-- Components -->
    <script src="../assets/js/components/academic-year-setup.js"></script>
    <script src="../assets/js/components/academic-year-management.js"></script>
    <script src="../assets/js/components/user-management.js"></script>
    <script src="../assets/js/components/assessment-management.js"></script>
    <script src="../assets/js/components/ca-configuration.js"></script>
    <script src="../assets/js/components/exam-types-management.js"></script>
    <script src="../assets/js/components/classes-streams.js"></script>
    <script src="../assets/js/components/grade-boundaries.js"></script>
    <script src="../assets/js/components/school-settings.js"></script>
    <script src="../assets/js/components/import-export-data.js"></script>
    <script src="../assets/js/components/student-management.js"></script>
    <script src="../assets/js/components/register-o-level-student.js"></script>
    <script src="../assets/js/components/register-a-level-student.js"></script>
    <script src="../assets/js/components/teacher-management.js"></script>
    <script src="../assets/js/components/o-level-report-cards.js"></script>

    <!-- Main Application -->
    <script src="../assets/js/main.js"></script>

    <!-- Global Sidebar Toggle Function -->
    <script>
        // Global sidebar toggle function
        function toggleSidebar() {
            if (window.Layout && window.Layout.toggleSidebar) {
                window.Layout.toggleSidebar();
            }
        }

        // Global functions for backward compatibility

        // Login error/success display functions
        function showLoginError(message) {
            const errorDiv = document.getElementById('login-error');
            const errorMessage = document.getElementById('login-error-message');
            if (errorDiv && errorMessage) {
                errorMessage.textContent = message;
                errorDiv.classList.remove('hidden');
                // Auto-hide success message if visible
                hideLoginSuccess();
            }
        }

        function hideLoginError() {
            const errorDiv = document.getElementById('login-error');
            if (errorDiv) {
                errorDiv.classList.add('hidden');
            }
        }

        function showLoginSuccess(message) {
            const successDiv = document.getElementById('login-success');
            const successMessage = document.getElementById('login-success-message');
            if (successDiv && successMessage) {
                successMessage.textContent = message;
                successDiv.classList.remove('hidden');
                // Auto-hide error message if visible
                hideLoginError();
            }
        }

        function hideLoginSuccess() {
            const successDiv = document.getElementById('login-success');
            if (successDiv) {
                successDiv.classList.add('hidden');
            }
        }

        // Password toggle function
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('password-toggle-icon');

            if (passwordInput && toggleIcon) {
                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    toggleIcon.classList.remove('fa-eye');
                    toggleIcon.classList.add('fa-eye-slash');
                } else {
                    passwordInput.type = 'password';
                    toggleIcon.classList.remove('fa-eye-slash');
                    toggleIcon.classList.add('fa-eye');
                }
            }
        }

        function showForgotPassword() {
            // Create a more professional modal instead of alert
            const modalHtml = `
                <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50 backdrop-blur-sm">
                    <div class="bg-white rounded-2xl shadow-xl max-w-md w-full p-6 transform transition-all">
                        <div class="text-center mb-6">
                            <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-key text-primary-600 text-2xl"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">Forgot Password?</h3>
                            <p class="text-gray-600">Contact your system administrator for password reset assistance.</p>
                        </div>
                        <div class="space-y-4">
                            <div class="bg-blue-50 border border-blue-200 rounded-xl p-4">
                                <div class="flex items-start">
                                    <i class="fas fa-info-circle text-blue-500 mt-0.5 mr-3"></i>
                                    <div>
                                        <p class="text-sm font-medium text-blue-800">Administrator Contact</p>
                                        <p class="text-sm text-blue-700 mt-1">Please reach out to your system administrator with your username and request a password reset.</p>
                                    </div>
                                </div>
                            </div>
                            <button onclick="closeForgotPasswordModal()" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-4 rounded-xl transition-colors duration-300">
                                <i class="fas fa-check mr-2"></i>
                                Got it
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        function closeForgotPasswordModal() {
            const modal = document.querySelector('.fixed.inset-0.z-50');
            if (modal) {
                modal.remove();
            }
        }

        function showLoginHelp() {
            // Create a more professional help modal
            const modalHtml = `
                <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50 backdrop-blur-sm">
                    <div class="bg-white rounded-2xl shadow-xl max-w-md w-full p-6 transform transition-all">
                        <div class="text-center mb-6">
                            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-life-ring text-green-600 text-2xl"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">Need Help?</h3>
                            <p class="text-gray-600">Get assistance with accessing your SmartReport account.</p>
                        </div>
                        <div class="space-y-4">
                            <div class="bg-green-50 border border-green-200 rounded-xl p-4">
                                <div class="flex items-start">
                                    <i class="fas fa-headset text-green-500 mt-0.5 mr-3"></i>
                                    <div>
                                        <p class="text-sm font-medium text-green-800">Technical Support</p>
                                        <p class="text-sm text-green-700 mt-1">Contact your system administrator for login assistance, account issues, or technical support.</p>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-blue-50 border border-blue-200 rounded-xl p-4">
                                <div class="flex items-start">
                                    <i class="fas fa-user-shield text-blue-500 mt-0.5 mr-3"></i>
                                    <div>
                                        <p class="text-sm font-medium text-blue-800">Account Access</p>
                                        <p class="text-sm text-blue-700 mt-1">Ensure you're using the correct username and password provided by your administrator.</p>
                                    </div>
                                </div>
                            </div>
                            <button onclick="closeLoginHelpModal()" class="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-4 rounded-xl transition-colors duration-200">
                                <i class="fas fa-check mr-2"></i>
                                Got it
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        function closeLoginHelpModal() {
            const modal = document.querySelector('.fixed.inset-0.z-50');
            if (modal) {
                modal.remove();
            }
        }

        // Note: Main application initialization is handled by main.js
        // which has its own DOMContentLoaded event listener
    </script>

    <!-- Scripts already loaded above -->
</body>
</html>

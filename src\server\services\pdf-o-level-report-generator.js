const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs').promises;

class PDFOLevelReportGenerator {
  constructor() {
    this.browser = null;
  }

  // Initialize browser instance
  async initBrowser() {
    if (!this.browser) {
      try {
        console.log('Initializing browser for PDF generation...');

        // Use puppeteer's bundled Chromium for reliability
        this.browser = await puppeteer.launch({
          headless: true,
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu',
            '--disable-web-security',
            '--allow-running-insecure-content',
            '--disable-features=VizDisplayCompositor'
          ]
        });

        console.log('Browser initialized successfully');
      } catch (error) {
        console.error('Failed to launch browser:', error);
        throw new Error(`PDF generation service unavailable: ${error.message}`);
      }
    }
    return this.browser;
  }

  // Close browser instance
  async closeBrowser() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  // Get Downloads directory path
  getDownloadsPath() {
    try {
      // Try Electron API first (for packaged app)
      const { app } = require('electron');
      return app.getPath('downloads');
    } catch (error) {
      // Fallback for development mode
      console.log('Electron not available, using fallback Downloads path');
      const os = require('os');
      return path.join(os.homedir(), 'Downloads');
    }
  }

  // Get the default school logo path
  getDefaultLogoPath() {
    // Use server URL for consistency with frontend
    return 'http://localhost:3001/assets/images/default-school-logo.png';
  }

  // Get school logo URL for PDF generation
  getSchoolLogoUrl(logoPath) {
    if (!logoPath) {
      return this.getDefaultLogoPath();
    }

    // If it's already a full URL, return as is
    if (logoPath.startsWith('http://') || logoPath.startsWith('https://')) {
      return logoPath;
    }

    // If it starts with a slash, it's relative to server root
    if (logoPath.startsWith('/')) {
      return `http://localhost:3001${logoPath}`;
    }

    // Otherwise, assume it's relative to the school uploads directory
    return `http://localhost:3001/assets/images/uploads/school/${logoPath}`;
  }

  // Get student photo URL for PDF generation
  getStudentPhotoUrl(photoPath) {
    if (!photoPath) {
      return null;
    }

    // If it's already a full URL, return as is
    if (photoPath.startsWith('http://') || photoPath.startsWith('https://')) {
      return photoPath;
    }

    // If it starts with a slash, it's relative to server root
    if (photoPath.startsWith('/')) {
      return `http://localhost:3001${photoPath}`;
    }

    // Otherwise, assume it's relative to the o-level students uploads directory
    return `http://localhost:3001/assets/images/uploads/o-level-students/${photoPath}`;
  }

  // Create unique directory name to avoid conflicts
  async createUniqueDirectory(basePath, baseName) {
    let dirName = baseName;
    let counter = 1;
    let fullPath = path.join(basePath, dirName);

    // Check if directory exists and create unique name if needed
    while (true) {
      try {
        await fs.access(fullPath);
        // Directory exists, try with counter
        dirName = `${baseName} (${counter})`;
        fullPath = path.join(basePath, dirName);
        counter++;
      } catch (error) {
        // Directory doesn't exist, we can use this name
        break;
      }
    }

    // Create the directory
    await fs.mkdir(fullPath, { recursive: true });
    return fullPath;
  }

  // Create unique file name to avoid conflicts
  async createUniqueFileName(dirPath, baseName, extension) {
    let fileName = `${baseName}.${extension}`;
    let counter = 1;
    let fullPath = path.join(dirPath, fileName);

    // Check if file exists and create unique name if needed
    while (true) {
      try {
        await fs.access(fullPath);
        // File exists, try with counter
        fileName = `${baseName} (${counter}).${extension}`;
        fullPath = path.join(dirPath, fileName);
        counter++;
      } catch (error) {
        // File doesn't exist, we can use this name
        break;
      }
    }

    return { fileName, fullPath };
  }

  // Generate O-Level report card PDF and save to Downloads
  async generateOLevelReportCard(reportData, academicContext) {
    console.log('🚀 Starting single PDF generation...');
    let page;

    try {
      const downloadsPath = this.getDownloadsPath();

      const browser = await this.initBrowser();
      page = await browser.newPage();

      // Set page size and margins for report card
      await page.setViewport({ width: 1200, height: 1600 });

      // Generate HTML content for the report card
      const htmlContent = this.generateReportCardHTML(reportData);

      // Set the HTML content
      await page.setContent(htmlContent, {
        waitUntil: 'networkidle0',
        timeout: 30000
      });

      // Generate PDF with specific options for report cards
      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '0.5in',
          right: '0.5in',
          bottom: '0.5in',
          left: '0.5in'
        },
        displayHeaderFooter: false,
        preferCSSPageSize: true
      });

      // Save to Downloads directory
      const student = reportData.student;
      const fileName = `${student.first_name}_${student.last_name}_${academicContext.academic_year_name}_${academicContext.term_name}`;

      const { fullPath } = await this.createUniqueFileName(downloadsPath, fileName, 'pdf');
      await fs.writeFile(fullPath, pdfBuffer);

      console.log('✅ PDF saved successfully:', fullPath);
      return {
        success: true,
        filePath: fullPath,
        fileName: path.basename(fullPath)
      };

    } catch (error) {
      console.error('❌ PDF generation error:', error);
      console.error('❌ Error stack:', error.stack);
      throw new Error(`Failed to generate PDF report card: ${error.message}`);
    } finally {
      if (page) {
        await page.close();
      }
    }
  }

  // Generate multiple report cards and save to Downloads folder
  async generateBulkOLevelReportCards(reportsData, academicContext, shouldCreateFolder = true) {
    console.log('🚀 Starting bulk PDF generation for', reportsData.length, 'students...');
    let page;

    try {
      const downloadsPath = this.getDownloadsPath();

      const browser = await this.initBrowser();
      page = await browser.newPage();
      await page.setViewport({ width: 1200, height: 1600 });

      let folderPath = downloadsPath;
      let folderName = null;

      if (shouldCreateFolder) {
        folderName = `${academicContext.class_name} Report Cards ${academicContext.academic_year_name}_${academicContext.term_name}`;
        folderPath = await this.createUniqueDirectory(downloadsPath, folderName);
        console.log('📂 Created folder:', folderPath);
      }

      // Save individual PDFs for each student
      const savedFiles = [];
      for (let i = 0; i < reportsData.length; i++) {
        const reportData = reportsData[i];
        const student = reportData.student;

        console.log(`📄 Generating PDF ${i + 1}/${reportsData.length} for ${student.first_name} ${student.last_name}...`);

        try {
          // Generate individual PDF using page
          const htmlContent = this.generateReportCardHTML(reportData);

          await page.setContent(htmlContent, {
            waitUntil: 'networkidle0',
            timeout: 30000
          });

          const pdfBuffer = await page.pdf({
            format: 'A4',
            printBackground: true,
            margin: {
              top: '0.5in',
              right: '0.5in',
              bottom: '0.5in',
              left: '0.5in'
            },
            displayHeaderFooter: false,
            preferCSSPageSize: true
          });

          const fileName = `${student.first_name}_${student.last_name}_Report_Card`;
          const { fullPath } = await this.createUniqueFileName(folderPath, fileName, 'pdf');

          await fs.writeFile(fullPath, pdfBuffer);

          savedFiles.push({
            studentName: `${student.first_name} ${student.last_name}`,
            filePath: fullPath,
            fileName: path.basename(fullPath)
          });

          console.log(`✅ Generated PDF for ${student.first_name} ${student.last_name}`);
        } catch (studentError) {
          console.error(`❌ Failed to generate PDF for ${student.first_name} ${student.last_name}:`, studentError.message);
          // Continue with other students
        }
      }

      console.log(`🎉 Bulk PDF generation completed. Generated ${savedFiles.length}/${reportsData.length} files.`);

      return {
        success: true,
        folderPath: shouldCreateFolder ? folderPath : downloadsPath,
        folderName: shouldCreateFolder ? path.basename(folderPath) : null,
        filesGenerated: savedFiles.length,
        files: savedFiles
      };

    } catch (error) {
      console.error('❌ Bulk PDF generation error:', error);
      console.error('❌ Error stack:', error.stack);
      throw new Error(`Failed to generate bulk PDF report cards: ${error.message}`);
    } finally {
      if (page) {
        await page.close();
      }
    }
  }



  // Generate HTML content for a single report card
  generateReportCardHTML(reportData) {
    if (!reportData) {
      throw new Error('Report data is required');
    }

    const { student = {} } = reportData;

    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Report Card - ${student.first_name || 'Student'} ${student.last_name || ''}</title>
        <style>
          ${this.getReportCardCSS()}
        </style>
      </head>
      <body>
        <div class="report-card">
          ${this.generateComprehensiveReportCardContent(reportData)}
        </div>
      </body>
      </html>
    `;
  }

  // Generate comprehensive report card content with new layout
  generateComprehensiveReportCardContent(reportData) {
    const {
      student = {},
      subjects = [],
      formativeAssessments = [],
      summativeAssessments = [],
      termAverages = [],
      classExamTypes = [],
      schoolSettings = {},
      academicContext = {},
      gradingScale = [],
      overallSummary = {}
    } = reportData || {};

    return `
      <!-- Header Section -->
      <div class="header">
        <div class="header-top">
          <!-- School Logo on the left -->
          <div class="logo">
            <img src="${this.getSchoolLogoUrl(schoolSettings?.school_logo)}" alt="School Logo" class="school-logo">
          </div>

          <!-- School Information on the right -->
          <div class="school-info">
            <h1 class="school-name">${schoolSettings?.school_name ? schoolSettings.school_name.toUpperCase() : 'SCHOOL NAME'}</h1>
            <div class="contact-info">
              <p>${schoolSettings?.school_address || 'School Address'}</p>
              <p>${schoolSettings?.school_phone ? schoolSettings.school_phone.split(',').map(phone => phone.trim()).join(', ') : '+256 xxx xxxxxx'}</p>
              <p>${schoolSettings?.school_email || '<EMAIL>'}</p>
              ${schoolSettings?.school_website ? `<p>${schoolSettings.school_website}</p>` : ''}
            </div>
          </div>
        </div>



        <!-- Report Title centered below -->
        <div class="report-title">
          <h2>END OF ${(academicContext?.term_name || 'TERM').toUpperCase()} ACADEMIC REPORT CARD ${(academicContext?.academic_year_name || 'YEAR').toUpperCase()}</h2>
        </div>
      </div>

      <!-- Student Information -->
      <div class="student-section">
        <div class="student-details">
          <div class="student-info-left">
            <div class="info-row">
              <span class="label">Student Name:</span> <span>${student.first_name || '-'} ${student.last_name || '-'}</span>
              <span class="label">Admission Number:</span> <span>${student.admission_number || '-'}</span>
            </div>
            <div class="info-row">
              <span class="label">Class:</span> <span>${academicContext?.class_name || '-'}</span>
              <span class="label">Stream:</span> <span>${academicContext?.stream_name || '-'}</span>
            </div>
          </div>

          <div class="student-photo">
            ${student.passport_photo ? `
              <img src="${this.getStudentPhotoUrl(student.passport_photo)}" alt="Student Photo" class="passport-photo">
            ` : `
              <div class="photo-placeholder">
                <span>No Photo</span>
              </div>
            `}
          </div>
        </div>
      </div>

      <!-- Comprehensive Assessment Table -->
      <div class="assessment-section">
        <table class="assessment-table">
          <thead>
            <tr class="main-header">
              <th rowspan="2" class="subject-header">SUBJECTS</th>
              <th colspan="8" class="formative-header">FORMATIVE ASSESSMENT</th>
              <th colspan="${this.getSummativeColumnsCount(classExamTypes)}" class="summative-header">SUMMATIVE ASSESSMENT</th>
              <th colspan="2" class="term-header">TERM AVERAGE</th>
              <th rowspan="2" class="initials-header">INITIALS</th>
            </tr>
            <tr class="sub-header">
              <!-- Formative Assessment Sub-headers -->
              <th>CA1</th>
              <th>CA2</th>
              <th>CA3</th>
              <th>CA4</th>
              <th>CA5</th>
              <th>CA6</th>
              <th>AVE</th>
              <th>Total<br>(Out of 20%)</th>

              <!-- Summative Assessment Sub-headers -->
              ${this.renderSummativeHeaders(classExamTypes)}

              <!-- Term Average Sub-headers -->
              <th>Total Mark<br>(20%) + (80%)</th>
              <th>Grade</th>
            </tr>
          </thead>
          <tbody>
            ${this.renderSubjectRows(subjects, formativeAssessments, summativeAssessments, termAverages, classExamTypes)}

            <!-- Totals and Averages Row -->
            <tr class="totals-row">
              <td class="totals-label">TOTALS/AVERAGES</td>
              ${this.renderTotalsRow(overallSummary, classExamTypes)}
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Grading Scale and Performance Summary -->
      <div class="summary-section">
        <div class="grading-scale">
          <h3>GRADING SCALE</h3>
          <div class="scale-content">
            ${this.renderGradingScale(gradingScale)}
          </div>
        </div>

        <div class="performance-summary">
          <h3>PERFORMANCE SUMMARY</h3>
          <div class="summary-content">
            <div class="summary-item"><span class="label">Subjects Taken:</span> <span>${overallSummary.subjectsTaken || 0}</span></div>
            <div class="summary-item"><span class="label">Total Marks:</span> <span>${overallSummary.totalMarks || 0}/${overallSummary.maxMarks || 0}</span></div>
            <div class="summary-item"><span class="label">Average:</span> <span class="highlight">${overallSummary.average || 0}%</span></div>
            <div class="summary-item"><span class="label">Overall Grade:</span> <span class="grade-highlight">${overallSummary.overallGrade || '-'}</span></div>
          </div>
        </div>
      </div>

      <!-- Comments Section -->
      <div class="comments-section">
        <h3>TEACHER COMMENTS</h3>
        <div class="comment-box">
          <span class="comment-label">Class Teacher's Comment:</span>
          <div class="comment-content">
            <!-- Space for handwritten comment -->
          </div>
        </div>

        <div class="comment-box">
          <span class="comment-label">Head Teacher's Comment:</span>
          <div class="comment-content">
            <!-- Space for handwritten comment -->
          </div>
        </div>
      </div>

      <!-- Administrative Information -->
      <div class="admin-section">
        <div class="admin-info">
          <div class="admin-item"><span class="label">Next Term Begins:</span> <span>${reportData.nextTerm ? new Date(reportData.nextTerm.start_date).toLocaleDateString() : 'TBD'}</span></div>
          <div class="admin-item"><span class="label">Date Printed:</span> <span>${new Date().toLocaleDateString()}</span></div>
        </div>

        <div class="stamp-section">
          <div class="stamp-box">
            <span class="stamp-label">SCHOOL STAMP/SEAL</span>
          </div>
        </div>
      </div>

      <!-- Signatures -->
      <div class="signatures-section">
        <div class="signature-box">
          <div class="signature-line"></div>
          <span class="signature-label">Class Teacher's Signature</span>
        </div>

        <div class="signature-box">
          <div class="signature-line"></div>
          <span class="signature-label">Headteacher's Signature</span>
        </div>
      </div>

      <!-- School Motto Footer -->
      ${schoolSettings?.school_motto ? `
        <div class="school-motto-footer">
          <p>"${schoolSettings.school_motto.charAt(0).toUpperCase() + schoolSettings.school_motto.slice(1).toLowerCase()}"</p>
        </div>
      ` : ''}
    `;
  }

  // Helper methods for table rendering
  getSummativeColumnsCount(classExamTypes) {
    return classExamTypes ? classExamTypes.length + 1 : 3; // +1 for total column
  }

  renderSummativeHeaders(classExamTypes) {
    if (!classExamTypes || classExamTypes.length === 0) {
      return `<th>No Exams<br>Configured</th>`;
    }

    let headers = '';
    classExamTypes.forEach(examType => {
      headers += `<th>${examType.short_name}<br>${examType.weight_percentage}%</th>`;
    });

    // Summative assessment is always out of 80 in the two-tier system
    headers += `<th>Total<br>(Out of 80%)</th>`;

    return headers;
  }

  renderSubjectRows(subjects, formativeAssessments, summativeAssessments, termAverages, classExamTypes) {
    if (!subjects || subjects.length === 0) return '';

    return subjects.map(subject => {
      const formative = formativeAssessments.find(fa => fa.subject_id === subject.id) || {};
      const summative = summativeAssessments.find(sa => sa.subject_id === subject.id) || {};
      const termAvg = termAverages.find(ta => ta.subject_id === subject.id) || {};

      return `
        <tr class="subject-row">
          <td class="subject-name">${subject.name || '-'}</td>

          <!-- Formative Assessment Columns -->
          <td class="ca-score">${formative.ca1 !== null && formative.ca1 !== undefined ? Number(formative.ca1).toFixed(1) : '-'}</td>
          <td class="ca-score">${formative.ca2 !== null && formative.ca2 !== undefined ? Number(formative.ca2).toFixed(1) : '-'}</td>
          <td class="ca-score">${formative.ca3 !== null && formative.ca3 !== undefined ? Number(formative.ca3).toFixed(1) : '-'}</td>
          <td class="ca-score">${formative.ca4 !== null && formative.ca4 !== undefined ? Number(formative.ca4).toFixed(1) : '-'}</td>
          <td class="ca-score">${formative.ca5 !== null && formative.ca5 !== undefined ? Number(formative.ca5).toFixed(1) : '-'}</td>
          <td class="ca-score">${formative.ca6 !== null && formative.ca6 !== undefined ? Number(formative.ca6).toFixed(1) : '-'}</td>
          <td class="ca-average">${formative.average !== null && formative.average !== undefined ? Number(formative.average).toFixed(1) : '-'}</td>
          <td class="ca-points">${formative.totalPoints !== null && formative.totalPoints !== undefined ? Number(formative.totalPoints).toFixed(1) : '-'}</td>

          <!-- Summative Assessment Columns -->
          ${this.renderSummativeColumns(summative, classExamTypes)}

          <!-- Term Average Columns -->
          <td class="total-mark">${termAvg.totalMark !== null && termAvg.totalMark !== undefined ? Number(termAvg.totalMark).toFixed(1) : '-'}</td>
          <td class="grade">${termAvg.grade || '-'}</td>

          <!-- Teacher Initials Column -->
          <td class="teacher-initials">${subject.teacher_initials || '-'}</td>
        </tr>
      `;
    }).join('');
  }

  renderSummativeColumns(summative, classExamTypes) {
    let columns = '';

    if (summative.examScores && summative.examScores.length > 0) {
      summative.examScores.forEach(score => {
        columns += `<td class="exam-score">${score !== null && score !== undefined ? Number(score).toFixed(1) : '-'}</td>`;
      });
    } else if (classExamTypes && classExamTypes.length > 0) {
      // Show empty cells for each configured exam type
      classExamTypes.forEach(() => {
        columns += `<td class="exam-score">-</td>`;
      });
    } else {
      // No exam types configured
      columns += `<td class="exam-score">-</td>`;
    }

    // Add total column
    columns += `<td class="summative-total">${summative.total !== null && summative.total !== undefined ? Number(summative.total).toFixed(1) : '-'}</td>`;

    return columns;
  }

  renderTotalsRow(overallSummary, classExamTypes) {
    const examColumnsCount = classExamTypes ? classExamTypes.length : 2;

    // Generate empty cells for formative totals (8 columns)
    let formativeTotals = '';
    for (let i = 0; i < 8; i++) {
      formativeTotals += '<td class="total-cell">-</td>';
    }

    // Generate empty cells for summative totals
    let summativeTotals = '';
    for (let i = 0; i < examColumnsCount + 1; i++) { // +1 for total column
      summativeTotals += '<td class="total-cell">-</td>';
    }

    return `
      ${formativeTotals}
      ${summativeTotals}
      <td class="overall-total">${overallSummary.totalMarks !== null && overallSummary.totalMarks !== undefined ? Number(overallSummary.totalMarks).toFixed(1) : '-'}</td>
      <td class="overall-grade">${overallSummary.overallGrade || '-'}</td>
      <td class="overall-initials">-</td>
    `;
  }

  renderGradingScale(gradingScale) {
    if (!gradingScale || gradingScale.length === 0) {
      return `<div class="scale-item">No grading scale configured</div>`;
    }

    return gradingScale.map(grade => `
      <div class="scale-item">
        <span class="grade-label">${grade.grade_letter} (${grade.grade_descriptor}):</span>
        <span>${grade.min_percentage}-${grade.max_percentage}%</span>
      </div>
    `).join('');
  }

  // Generate CSS styles for the comprehensive report card
  getReportCardCSS() {
    return `
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Times New Roman', serif;
        font-size: 12px;
        line-height: 1.4;
        color: #000;
        background: white;
      }

      .report-card {
        max-width: 210mm;
        margin: 0 auto;
        padding: 10mm;
        background: white;
        position: relative;
      }

      /* Header Styles */
      .header {
        border-bottom: 2px solid #000;
        padding-bottom: 15px;
        margin-bottom: 20px;
      }

      .header-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 15px;
      }

      .logo {
        flex-shrink: 0;
      }

      .logo-placeholder {
        width: 80px;
        height: 80px;
      }

      .school-logo {
        width: 80px;
        height: 80px;
        object-fit: contain;
      }

      .school-info {
        text-align: right;
      }

      .school-name {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 6px;
        color: #000;
      }

      .contact-info {
        font-size: 12px;
        line-height: 1.4;
      }

      .contact-info p {
        margin-bottom: 3px;
      }

      .report-title {
        text-align: center;
        margin-top: 15px;
      }

      .report-title h2 {
        font-size: 18px;
        font-weight: bold;
        margin: 0;
        text-decoration: underline;
      }

      .school-motto {
        text-align: center;
        margin-bottom: 15px;
      }

      .school-motto p {
        font-size: 12px;
        font-style: italic;
        color: #666;
        margin: 0;
      }



      /* Student Information Styles */
      .student-section {
        margin-bottom: 20px;
        background-color: #f8f8f8;
        padding: 15px;
        border: 1px solid #ccc;
      }

      .student-details {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
      }

      .student-info-left {
        flex: 1;
        margin-right: 20px;
      }

      .info-row {
        display: grid;
        grid-template-columns: auto 1fr auto 1fr;
        gap: 15px;
        margin-bottom: 8px;
        font-size: 11px;
        align-items: center;
      }

      .info-row .label {
        font-weight: 600;
        color: #333;
      }

      .student-photo {
        flex-shrink: 0;
        width: 100px;
        height: 120px;
        border: 2px solid #333;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: white;
      }

      .passport-photo {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .photo-placeholder {
        font-size: 10px;
        color: #666;
        text-align: center;
      }

      /* Assessment Table Styles */
      .assessment-section {
        margin-bottom: 20px;
      }

      .assessment-section h3 {
        font-size: 14px;
        font-weight: bold;
        border-bottom: 1px solid #666;
        margin-bottom: 10px;
        padding-bottom: 3px;
      }

      .assessment-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 10px;
        margin-bottom: 15px;
      }

      .assessment-table th,
      .assessment-table td {
        border: 1px solid #000;
        padding: 4px 2px;
        text-align: center;
        vertical-align: middle;
      }

      .main-header th {
        background-color: #f0f0f0;
        font-weight: bold;
        font-size: 9px;
      }

      .sub-header th {
        background-color: #f8f8f8;
        font-weight: bold;
        font-size: 8px;
      }

      .subject-header {
        background-color: #e0e0e0 !important;
        font-weight: bold;
        width: 120px;
      }

      .formative-header {
        background-color: #e3f2fd !important;
      }

      .summative-header {
        background-color: #e8f5e8 !important;
      }

      .term-header {
        background-color: #fff3e0 !important;
      }

      .initials-header {
        background-color: #f3e5f5 !important;
      }

      .subject-row td {
        font-size: 9px;
      }

      .subject-name {
        text-align: left !important;
        font-weight: 500;
        padding-left: 8px !important;
      }

      .ca-score,
      .exam-score {
        font-size: 9px;
      }

      .ca-average,
      .ca-points,
      .summative-total,
      .total-mark,
      .grade {
        font-weight: 600;
      }

      .teacher-initials {
        font-weight: 500;
        font-size: 9px;
      }

      .totals-row {
        background-color: #f0f0f0;
        font-weight: bold;
      }

      .totals-label {
        text-align: center !important;
        font-weight: bold;
      }

      /* Summary Section Styles */
      .summary-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 20px;
      }

      .grading-scale h3,
      .performance-summary h3 {
        font-size: 14px;
        font-weight: bold;
        border-bottom: 1px solid #666;
        margin-bottom: 10px;
        padding-bottom: 3px;
      }

      .scale-content,
      .summary-content {
        font-size: 11px;
      }

      .scale-item,
      .summary-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 3px;
      }

      .grade-label,
      .summary-item .label {
        font-weight: 600;
      }

      .highlight {
        font-weight: bold;
        font-size: 12px;
      }

      .grade-highlight {
        font-weight: bold;
        font-size: 14px;
      }

      /* Comments Section Styles */
      .comments-section {
        margin-bottom: 20px;
      }

      .comments-section h3 {
        font-size: 14px;
        font-weight: bold;
        border-bottom: 1px solid #666;
        margin-bottom: 10px;
        padding-bottom: 3px;
      }

      .comment-box {
        margin-bottom: 15px;
      }

      .comment-label {
        font-weight: 600;
        font-size: 11px;
      }

      .comment-content {
        border: 1px solid #666;
        padding: 10px;
        margin-top: 5px;
        min-height: 60px;
        background-color: #fafafa;
        font-size: 11px;
        line-height: 1.4;
      }

      /* Administrative Section Styles */
      .admin-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 20px;
      }

      .admin-info {
        font-size: 11px;
      }

      .admin-item {
        display: flex;
        margin-bottom: 5px;
      }

      .admin-item .label {
        font-weight: 600;
        width: 120px;
        flex-shrink: 0;
      }

      .stamp-section {
        text-align: center;
      }

      .stamp-box {
        border: 1px solid #666;
        padding: 30px 20px;
        background-color: #fafafa;
      }

      .stamp-label {
        font-size: 10px;
        font-weight: 600;
      }

      /* Signatures Section Styles */
      .signatures-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        border-top: 1px solid #666;
        padding-top: 15px;
      }

      .signature-box {
        text-align: center;
      }

      .signature-line {
        border-bottom: 1px solid #666;
        margin-bottom: 10px;
        height: 40px;
      }

      .signature-label {
        font-size: 11px;
        font-weight: 600;
      }

      /* School Motto Footer */
      .school-motto-footer {
        text-align: center;
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid #ccc;
      }

      .school-motto-footer p {
        font-size: 12px;
        font-style: italic;
        color: #666;
        margin: 0;
      }

      /* Print Styles */
      @media print {
        .report-card {
          margin: 0;
          padding: 5mm;
        }

        body {
          font-size: 11px;
        }

        .assessment-table {
          font-size: 9px;
        }

        .subject-row td {
          font-size: 8px;
        }
      }

      /* Page Break Styles for Bulk Reports */
      .page-break {
        page-break-before: always;
      }
    `;
  }


}

module.exports = PDFOLevelReportGenerator;

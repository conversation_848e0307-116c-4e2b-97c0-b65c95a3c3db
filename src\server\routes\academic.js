const express = require('express');
const { executeQuery } = require('../../database/connection');
const { authenticateToken } = require('./auth');
const { requireAcademicContext } = require('../middleware/validation');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// =============================================
// ACADEMIC YEARS ROUTES
// =============================================

// Get all academic years
router.get('/years', async (req, res) => {
  try {
    const query = `
      SELECT
        ay.id, ay.name, ay.start_date, ay.end_date, ay.is_active,
        ay.created_at, ay.updated_at
      FROM academic_years ay
      ORDER BY ay.name DESC
    `;

    const result = await executeQuery(query);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get academic years error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve academic years'
    });
  }
});

// Create academic year
router.post('/years', async (req, res) => {
  try {
    const { name, start_date, end_date, is_active, terms } = req.body;

    // Validate required fields
    if (!name || !start_date || !end_date) {
      return res.status(400).json({
        success: false,
        message: 'Name, start date, and end date are required'
      });
    }

    // If setting as active, deactivate other years
    if (is_active) {
      await executeQuery('UPDATE academic_years SET is_active = FALSE');
    }

    const insertQuery = `
      INSERT INTO academic_years (name, start_date, end_date, is_active)
      VALUES (?, ?, ?, ?)
    `;

    const result = await executeQuery(insertQuery, [name, start_date, end_date, is_active || false]);

    if (!result.success) {
      throw new Error(result.error);
    }

    const academicYearId = result.data.insertId;

    // Create terms if provided
    if (terms && Array.isArray(terms) && terms.length > 0) {
      // Deactivate all existing terms if we're creating new ones
      await executeQuery('UPDATE terms SET is_active = FALSE');

      for (const term of terms) {
        const { name: termName, number, start_date: termStart, end_date: termEnd, is_active: termActive } = term;

        if (termName && number && termStart && termEnd) {
          const termInsertQuery = `
            INSERT INTO terms (name, number, academic_year_id, start_date, end_date, is_active)
            VALUES (?, ?, ?, ?, ?, ?)
          `;

          await executeQuery(termInsertQuery, [
            termName,
            number,
            academicYearId,
            termStart,
            termEnd,
            termActive || false
          ]);
        }
      }
    }

    // Get the created academic year with terms
    const newYearQuery = `
      SELECT ay.*,
        (SELECT COUNT(*) FROM terms WHERE academic_year_id = ay.id) as terms_count,
        (SELECT COUNT(*) FROM terms WHERE academic_year_id = ay.id AND is_active = TRUE) as active_terms_count
      FROM academic_years ay
      WHERE ay.id = ?
    `;
    const newYearResult = await executeQuery(newYearQuery, [academicYearId]);

    res.status(201).json({
      success: true,
      message: 'Academic year created successfully',
      data: newYearResult.data[0]
    });

  } catch (error) {
    console.error('Create academic year error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create academic year'
    });
  }
});

// Update academic year
router.put('/years/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, start_date, end_date, is_active } = req.body;

    // Check if academic year exists
    const checkQuery = 'SELECT id FROM academic_years WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Academic year not found'
      });
    }

    // If setting as active, deactivate other years
    if (is_active) {
      await executeQuery('UPDATE academic_years SET is_active = FALSE WHERE id != ?', [id]);
    }

    const updateQuery = `
      UPDATE academic_years SET
        name = ?, start_date = ?, end_date = ?, is_active = ?
      WHERE id = ?
    `;

    const result = await executeQuery(updateQuery, [name, start_date, end_date, is_active || false, id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get updated academic year
    const updatedYearQuery = 'SELECT * FROM academic_years WHERE id = ?';
    const updatedYearResult = await executeQuery(updatedYearQuery, [id]);

    res.json({
      success: true,
      message: 'Academic year updated successfully',
      data: updatedYearResult.data[0]
    });

  } catch (error) {
    console.error('Update academic year error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update academic year'
    });
  }
});

// =============================================
// TERMS ROUTES
// =============================================

// Get terms (optionally filtered by academic year)
router.get('/terms', async (req, res) => {
  try {
    const { academic_year_id } = req.query;

    let query = `
      SELECT
        t.*,
        ay.name as academic_year_name
      FROM terms t
      LEFT JOIN academic_years ay ON t.academic_year_id = ay.id
    `;

    let params = [];

    if (academic_year_id) {
      query += ' WHERE t.academic_year_id = ?';
      params.push(academic_year_id);
    }

    query += ' ORDER BY t.academic_year_id DESC, t.number ASC';
    
    const result = await executeQuery(query, params);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get terms error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve terms'
    });
  }
});

// Create term
router.post('/terms', async (req, res) => {
  try {
    const { name, number, academic_year_id, start_date, end_date, is_active } = req.body;

    // Validate required fields
    if (!name || !number || !academic_year_id || !start_date || !end_date) {
      return res.status(400).json({
        success: false,
        message: 'Name, number, academic year, start date, and end date are required'
      });
    }

    // If setting as active, deactivate other terms
    if (is_active) {
      await executeQuery('UPDATE terms SET is_active = FALSE');
    }

    const insertQuery = `
      INSERT INTO terms (name, number, academic_year_id, start_date, end_date, is_active)
      VALUES (?, ?, ?, ?, ?, ?)
    `;

    const result = await executeQuery(insertQuery, [name, number, academic_year_id, start_date, end_date, is_active || false]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get the created term with academic year info
    const newTermQuery = `
      SELECT t.*, ay.name as academic_year_name
      FROM terms t
      LEFT JOIN academic_years ay ON t.academic_year_id = ay.id
      WHERE t.id = ?
    `;
    const newTermResult = await executeQuery(newTermQuery, [result.data.insertId]);

    res.status(201).json({
      success: true,
      message: 'Term created successfully',
      data: newTermResult.data[0]
    });

  } catch (error) {
    console.error('Create term error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create term'
    });
  }
});

// Update term
router.put('/terms/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, number, academic_year_id, start_date, end_date, is_active } = req.body;

    // Check if term exists
    const checkQuery = 'SELECT id FROM terms WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Term not found'
      });
    }

    // If setting as active, deactivate other terms
    if (is_active) {
      await executeQuery('UPDATE terms SET is_active = FALSE WHERE id != ?', [id]);
    }

    const updateQuery = `
      UPDATE terms SET
        name = ?, number = ?, academic_year_id = ?, start_date = ?, end_date = ?, is_active = ?
      WHERE id = ?
    `;

    const result = await executeQuery(updateQuery, [name, number, academic_year_id, start_date, end_date, is_active || false, id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get updated term
    const updatedTermQuery = `
      SELECT t.*, ay.name as academic_year_name
      FROM terms t
      LEFT JOIN academic_years ay ON t.academic_year_id = ay.id
      WHERE t.id = ?
    `;
    const updatedTermResult = await executeQuery(updatedTermQuery, [id]);

    res.json({
      success: true,
      message: 'Term updated successfully',
      data: updatedTermResult.data[0]
    });

  } catch (error) {
    console.error('Update term error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update term'
    });
  }
});

// Activate term
router.patch('/terms/:id/activate', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if term exists
    const checkQuery = 'SELECT id, name, academic_year_id FROM terms WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Term not found'
      });
    }

    // Delete term
    const term = checkResult.data[0];

    // Deactivate all other terms
    await executeQuery('UPDATE terms SET is_active = FALSE');

    // Activate the specified term
    const activateQuery = 'UPDATE terms SET is_active = TRUE WHERE id = ?';
    const deleteResult = await executeQuery(activateQuery, [id]);

    if (!deleteResult.success) {
      throw new Error(deleteResult.error);
    }

    res.json({
      success: true,
      message: `${term.name} has been activated successfully`,
      data: { id: term.id, name: term.name }
    });

  } catch (error) {
    console.error('Activate term error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to activate term'
    });
  }
});

// Delete term
router.delete('/terms/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if term exists
    const checkQuery = 'SELECT id FROM terms WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Term not found'
      });
    }

    const deleteQuery = 'DELETE FROM terms WHERE id = ?';
    const result = await executeQuery(deleteQuery, [id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      message: 'Term deleted successfully'
    });

  } catch (error) {
    console.error('Delete term error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete term'
    });
  }
});

// =============================================
// SUBJECTS ROUTES
// =============================================

// Get O-Level subjects with optional filters
router.get('/subjects/o-level', async (req, res) => {
  try {
    const { subject_type, class_level_id } = req.query;

    let query = '';
    let params = [];

    // If class_level_id is provided, join with subject-class relationships
    if (class_level_id) {
      query = `
        SELECT DISTINCT
          s.id, s.name, s.short_name, s.subject_type, s.uneb_code, s.exam_papers,
          s.is_active, s.created_at, s.updated_at,
          sc.subject_status
        FROM o_level_subjects s
        INNER JOIN o_level_subject_classes sc ON s.id = sc.subject_id
        WHERE sc.class_level_id = ? AND s.is_active = TRUE
      `;
      params.push(class_level_id);
    } else {
      // If no class_level_id, load all subjects without subject_status to avoid duplicates
      query = `
        SELECT
          s.id, s.name, s.short_name, s.subject_type, s.uneb_code, s.exam_papers,
          s.is_active, s.created_at, s.updated_at,
          NULL as subject_status
        FROM o_level_subjects s
        WHERE s.is_active = TRUE
      `;
    }

    if (subject_type) {
      query += ' AND s.subject_type = ?';
      params.push(subject_type);
    }

    query += ' ORDER BY s.subject_type, s.name';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get O-Level subjects error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve O-Level subjects'
    });
  }
});

// Get A-Level subjects with optional filters
router.get('/subjects/a-level', async (req, res) => {
  try {
    const { subject_type } = req.query;

    // For A-Level, always load ALL subjects (not filtered by class level or stream)
    let query = `
      SELECT
        s.id, s.name, s.short_name, s.subject_type, s.uneb_code,
        s.is_active, s.created_at, s.updated_at
      FROM a_level_subjects s
      WHERE s.is_active = TRUE
    `;

    let params = [];

    if (subject_type) {
      query += ' AND s.subject_type = ?';
      params.push(subject_type);
    }

    query += ' ORDER BY s.subject_type, s.name';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get A-Level subjects error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve A-Level subjects'
    });
  }
});

// Create O-Level subject
router.post('/subjects/o-level', async (req, res) => {
  try {
    const {
      name, short_name, subject_type, uneb_code, exam_papers
    } = req.body;

    // Validate required fields
    if (!name || !short_name || !subject_type) {
      return res.status(400).json({
        success: false,
        message: 'Name, short name, and subject type are required'
      });
    }

    const insertQuery = `
      INSERT INTO o_level_subjects (
        name, short_name, subject_type, uneb_code, exam_papers,
        is_active, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, TRUE, NOW(), NOW())
    `;

    const result = await executeQuery(insertQuery, [
      name, short_name, subject_type, uneb_code, exam_papers || 1
    ]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get the created subject
    const newSubjectQuery = 'SELECT * FROM o_level_subjects WHERE id = ?';
    const newSubjectResult = await executeQuery(newSubjectQuery, [result.data.insertId]);

    res.status(201).json({
      success: true,
      message: 'O-Level subject created successfully',
      data: newSubjectResult.data[0]
    });

  } catch (error) {
    console.error('Create O-Level subject error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create O-Level subject'
    });
  }
});

// Create A-Level subject
router.post('/subjects/a-level', async (req, res) => {
  try {
    const {
      name, short_name, subject_type, uneb_code
    } = req.body;

    // Validate required fields
    if (!name || !short_name || !subject_type) {
      return res.status(400).json({
        success: false,
        message: 'Name, short name, and subject type are required'
      });
    }

    const insertQuery = `
      INSERT INTO a_level_subjects (
        name, short_name, subject_type, uneb_code,
        is_active, created_at, updated_at
      ) VALUES (?, ?, ?, ?, TRUE, NOW(), NOW())
    `;

    const result = await executeQuery(insertQuery, [
      name, short_name, subject_type, uneb_code
    ]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get the created subject
    const newSubjectQuery = 'SELECT * FROM a_level_subjects WHERE id = ?';
    const newSubjectResult = await executeQuery(newSubjectQuery, [result.data.insertId]);

    res.status(201).json({
      success: true,
      message: 'A-Level subject created successfully',
      data: newSubjectResult.data[0]
    });

  } catch (error) {
    console.error('Create A-Level subject error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create A-Level subject'
    });
  }
});

// Update O-Level subject
router.put('/subjects/o-level/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name, short_name, subject_type, uneb_code, exam_papers, is_active
    } = req.body;

    // Check if subject exists
    const checkQuery = 'SELECT id FROM o_level_subjects WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'O-Level subject not found'
      });
    }

    const updateQuery = `
      UPDATE o_level_subjects SET
        name = ?, short_name = ?, subject_type = ?, uneb_code = ?,
        exam_papers = ?, is_active = ?
      WHERE id = ?
    `;

    const result = await executeQuery(updateQuery, [
      name, short_name, subject_type, uneb_code, exam_papers, is_active, id
    ]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get updated subject
    const updatedSubjectQuery = 'SELECT * FROM o_level_subjects WHERE id = ?';
    const updatedSubjectResult = await executeQuery(updatedSubjectQuery, [id]);

    res.json({
      success: true,
      message: 'O-Level subject updated successfully',
      data: updatedSubjectResult.data[0]
    });

  } catch (error) {
    console.error('Update O-Level subject error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update O-Level subject'
    });
  }
});

// Update A-Level subject
router.put('/subjects/a-level/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name, short_name, subject_type, uneb_code, is_active
    } = req.body;

    // Check if subject exists
    const checkQuery = 'SELECT id FROM a_level_subjects WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'A-Level subject not found'
      });
    }

    const updateQuery = `
      UPDATE a_level_subjects SET
        name = ?, short_name = ?, subject_type = ?, uneb_code = ?, is_active = ?
      WHERE id = ?
    `;

    const result = await executeQuery(updateQuery, [
      name, short_name, subject_type, uneb_code, is_active, id
    ]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get updated subject
    const updatedSubjectQuery = 'SELECT * FROM a_level_subjects WHERE id = ?';
    const updatedSubjectResult = await executeQuery(updatedSubjectQuery, [id]);

    res.json({
      success: true,
      message: 'A-Level subject updated successfully',
      data: updatedSubjectResult.data[0]
    });

  } catch (error) {
    console.error('Update A-Level subject error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update A-Level subject'
    });
  }
});

// =============================================
// A-LEVEL SUBJECT PAPERS ROUTES
// =============================================

// Get A-Level subject papers with optional filters
router.get('/subjects/a-level/:subjectId/papers', async (req, res) => {
  try {
    const { subjectId } = req.params;

    const query = `
      SELECT
        sp.id, sp.subject_id, sp.paper_number, sp.paper_name,
        sp.is_active, sp.created_at, sp.updated_at,
        s.name as subject_name, s.short_name as subject_short_name
      FROM a_level_subject_papers sp
      JOIN a_level_subjects s ON sp.subject_id = s.id
      WHERE sp.subject_id = ? AND sp.is_active = TRUE
      ORDER BY sp.paper_number
    `;

    const result = await executeQuery(query, [subjectId]);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data,
      message: 'A-Level subject papers retrieved successfully'
    });

  } catch (error) {
    console.error('Get A-Level subject papers error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve A-Level subject papers',
      error: error.message
    });
  }
});

// Get all A-Level subject papers (for admin purposes)
router.get('/subject-papers/a-level', async (req, res) => {
  try {
    const query = `
      SELECT
        sp.id, sp.subject_id, sp.paper_number, sp.paper_name,
        sp.is_active, sp.created_at, sp.updated_at,
        s.name as subject_name, s.short_name as subject_short_name,
        s.subject_type, s.uneb_code
      FROM a_level_subject_papers sp
      JOIN a_level_subjects s ON sp.subject_id = s.id
      WHERE sp.is_active = TRUE
      ORDER BY s.name, sp.paper_number
    `;

    const result = await executeQuery(query);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data,
      message: 'All A-Level subject papers retrieved successfully'
    });

  } catch (error) {
    console.error('Get all A-Level subject papers error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve A-Level subject papers',
      error: error.message
    });
  }
});

// =============================================
// LEVELS ROUTES
// =============================================

// Get all education levels (O Level, A Level)
router.get('/levels', async (req, res) => {
  try {
    const query = `
      SELECT id, code, name, display_name, sort_order, is_active
      FROM education_levels
      WHERE is_active = TRUE
      ORDER BY sort_order
    `;

    const result = await executeQuery(query);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get education levels error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve education levels'
    });
  }
});

// Get all class levels (S.1, S.2, S.3, S.4, S.5, S.6)
router.get('/class-levels', async (req, res) => {
  try {
    const query = `
      SELECT cl.id, cl.code, cl.name, cl.display_name, cl.sort_order, cl.streams_optional, cl.is_active,
             el.code as education_level_code, el.name as education_level_name
      FROM class_levels cl
      JOIN education_levels el ON cl.education_level_id = el.id
      WHERE cl.is_active = TRUE
      ORDER BY cl.sort_order
    `;

    const result = await executeQuery(query);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get class levels error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve class levels'
    });
  }
});

// =============================================
// STREAMS ROUTES
// =============================================

// Get streams with optional filters
router.get('/streams', async (req, res) => {
  try {
    const { stream_type } = req.query;

    let query = `
      SELECT
        s.id, s.name, s.stream_type, s.created_at, s.updated_at,
        GROUP_CONCAT(cl.name ORDER BY cl.sort_order SEPARATOR ', ') as class_names,
        GROUP_CONCAT(cl.id ORDER BY cl.sort_order) as class_level_ids,
        COUNT(DISTINCT cl.id) as class_count
      FROM streams s
      LEFT JOIN stream_classes sc ON s.id = sc.stream_id
      LEFT JOIN class_levels cl ON sc.class_level_id = cl.id AND cl.is_active = TRUE
      WHERE 1=1
    `;

    let params = [];

    // For management interface, show all streams (active and inactive)
    // Only filter by stream_type if specified
    if (stream_type) {
      query += ' AND s.stream_type = ?';
      params.push(stream_type);
    }

    query += ' GROUP BY s.id, s.name, s.stream_type, s.created_at, s.updated_at';
    query += ' ORDER BY s.stream_type, s.name';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get streams error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve streams'
    });
  }
});

// Create stream
router.post('/streams', async (req, res) => {
  try {
    const { name, stream_type, class_level_ids } = req.body;

    // Validate required fields
    if (!name || !stream_type) {
      return res.status(400).json({
        success: false,
        message: 'Name and stream type are required'
      });
    }

    // For O-Level streams, class_level_ids are required
    if (stream_type === 'o_level' && (!class_level_ids || !Array.isArray(class_level_ids) || class_level_ids.length === 0)) {
      return res.status(400).json({
        success: false,
        message: 'Class level IDs are required for O-Level streams'
      });
    }

    // Always create a new stream - allow same names for different class assignments
    // The uniqueness is enforced at the stream-class relationship level, not at the stream level
    const insertQuery = `
      INSERT INTO streams (name, stream_type)
      VALUES (?, ?)
    `;

    const result = await executeQuery(insertQuery, [name, stream_type]);

    if (!result.success) {
      throw new Error(result.error);
    }

    const streamId = result.data.insertId;



    // Create stream-class relationships for O-Level streams
    if (stream_type === 'o_level' && class_level_ids && class_level_ids.length > 0) {
      const relationshipPromises = class_level_ids.map(classLevelId => {
        return executeQuery(
          'INSERT INTO stream_classes (stream_id, class_level_id) VALUES (?, ?)',
          [streamId, classLevelId]
        );
      });

      const relationshipResults = await Promise.all(relationshipPromises);

      // Check if all relationships were created successfully
      const failedRelationships = relationshipResults.filter(r => !r.success);
      if (failedRelationships.length > 0) {
        // Rollback: delete the stream if relationships failed
        await executeQuery('DELETE FROM streams WHERE id = ?', [streamId]);
        throw new Error('Failed to create stream-class relationships');
      }
    }

    // Get the created stream with class information
    const newStreamQuery = `
      SELECT
        s.*,
        GROUP_CONCAT(cl.name ORDER BY cl.sort_order) as class_names,
        GROUP_CONCAT(cl.id ORDER BY cl.sort_order) as class_level_ids
      FROM streams s
      LEFT JOIN stream_classes sc ON s.id = sc.stream_id
      LEFT JOIN class_levels cl ON sc.class_level_id = cl.id
      WHERE s.id = ?
      GROUP BY s.id
    `;
    const newStreamResult = await executeQuery(newStreamQuery, [streamId]);

    res.status(201).json({
      success: true,
      message: 'Stream created successfully',
      data: newStreamResult.data[0]
    });

  } catch (error) {
    console.error('Create stream error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create stream'
    });
  }
});

// Update stream
router.put('/streams/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, stream_type, class_level_ids } = req.body;

    console.log('🔄 Updating stream:', { id, name, stream_type, class_level_ids });

    // Check if stream exists
    const checkQuery = 'SELECT id, name FROM streams WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Stream not found'
      });
    }

    const currentStream = checkResult.data[0];

    // Check if another stream with the same name (case-insensitive) already exists
    if (name && name.trim() !== '') {
      const duplicateCheck = await executeQuery(
        'SELECT id, name FROM streams WHERE LOWER(name) = LOWER(?) AND stream_type = ? AND id != ?',
        [name, stream_type, id]
      );

      if (duplicateCheck.success && duplicateCheck.data.length > 0) {
        const existingStreamName = duplicateCheck.data[0].name;
        return res.status(400).json({
          success: false,
          message: `A stream with name "${existingStreamName}" already exists. Stream names are case-insensitive. Please choose a different name.`
        });
      }
    }

    // Update stream basic info
    const updateQuery = `
      UPDATE streams SET
        name = ?, stream_type = ?
      WHERE id = ?
    `;

    const result = await executeQuery(updateQuery, [name, stream_type, id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Update stream-class relationships if class_level_ids is provided (for O-Level streams)
    if (stream_type === 'o_level' && class_level_ids !== undefined) {
      console.log('🔗 Updating stream-class relationships for stream:', id);

      // First, delete existing relationships
      const deleteRelationshipsQuery = 'DELETE FROM stream_classes WHERE stream_id = ?';
      const deleteResult = await executeQuery(deleteRelationshipsQuery, [id]);

      if (!deleteResult.success) {
        console.error('Failed to delete existing stream-class relationships:', deleteResult.error);
        throw new Error('Failed to update stream-class relationships');
      }

      console.log('🗑️ Deleted existing relationships for stream:', id);

      // Create new relationships if class_level_ids is provided and not empty
      if (Array.isArray(class_level_ids) && class_level_ids.length > 0) {
        const relationshipPromises = class_level_ids.map(classLevelId => {
          return executeQuery(
            'INSERT INTO stream_classes (stream_id, class_level_id) VALUES (?, ?)',
            [id, classLevelId]
          );
        });

        const relationshipResults = await Promise.all(relationshipPromises);

        // Check if all relationships were created successfully
        const failedRelationships = relationshipResults.filter(r => !r.success);
        if (failedRelationships.length > 0) {
          console.error('Failed to create some stream-class relationships:', failedRelationships);
          throw new Error('Failed to create stream-class relationships');
        }

        console.log('✅ Created new relationships for stream:', id, 'with classes:', class_level_ids);
      } else {
        console.log('📝 No class relationships to create (empty class_level_ids)');
      }
    }

    // Get updated stream with relationships
    const updatedStreamQuery = `
      SELECT s.*,
             GROUP_CONCAT(DISTINCT sc.class_level_id) as class_level_ids,
             GROUP_CONCAT(DISTINCT cl.name ORDER BY cl.name) as class_names
      FROM streams s
      LEFT JOIN stream_classes sc ON s.id = sc.stream_id
      LEFT JOIN class_levels cl ON sc.class_level_id = cl.id
      WHERE s.id = ?
      GROUP BY s.id
    `;
    const updatedStreamResult = await executeQuery(updatedStreamQuery, [id]);

    res.json({
      success: true,
      message: 'Stream updated successfully',
      data: updatedStreamResult.data[0]
    });

  } catch (error) {
    console.error('Update stream error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update stream'
    });
  }
});


// Delete stream
router.delete('/streams/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if stream exists
    const checkQuery = 'SELECT id, name, stream_type FROM streams WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Stream not found'
      });
    }

    const stream = checkResult.data[0];

    // For O-Level streams, implement the business rule:
    // - If a class has exactly 2 streams, deleting one should delete both
    // - If a class has 3+ streams, allow individual deletion
    let streamsToDelete = [id];
    let additionalStreamsDeleted = [];

    if (stream.stream_type === 'o_level') {
      // Get all classes that this stream is assigned to
      const classesQuery = `
        SELECT DISTINCT sc.class_level_id, cl.name as class_name
        FROM stream_classes sc
        JOIN class_levels cl ON sc.class_level_id = cl.id
        WHERE sc.stream_id = ?
      `;
      const classesResult = await executeQuery(classesQuery, [id]);

      if (classesResult.success && classesResult.data.length > 0) {
        for (const classInfo of classesResult.data) {
          // Count total streams for this class
          const streamCountQuery = `
            SELECT COUNT(DISTINCT sc.stream_id) as stream_count,
                   GROUP_CONCAT(DISTINCT s.id) as stream_ids,
                   GROUP_CONCAT(DISTINCT s.name) as stream_names
            FROM stream_classes sc
            JOIN streams s ON sc.stream_id = s.id
            WHERE sc.class_level_id = ? AND s.stream_type = 'o_level'
          `;
          const streamCountResult = await executeQuery(streamCountQuery, [classInfo.class_level_id]);

          if (streamCountResult.success && streamCountResult.data.length > 0) {
            const streamCount = streamCountResult.data[0].stream_count;
            const allStreamIds = streamCountResult.data[0].stream_ids ? streamCountResult.data[0].stream_ids.split(',') : [];
            const allStreamNames = streamCountResult.data[0].stream_names ? streamCountResult.data[0].stream_names.split(',') : [];

            console.log(`📊 Class ${classInfo.class_name} has ${streamCount} streams`);

            // If exactly 2 streams, delete both (business rule)
            if (streamCount === 2) {
              const otherStreamIds = allStreamIds.filter(streamId => streamId != id);
              streamsToDelete = [...streamsToDelete, ...otherStreamIds];

              // Track additional streams for reporting
              for (let i = 0; i < otherStreamIds.length; i++) {
                additionalStreamsDeleted.push({
                  id: otherStreamIds[i],
                  name: allStreamNames[allStreamIds.indexOf(otherStreamIds[i])],
                  class_name: classInfo.class_name
                });
              }

              console.log(`⚠️ Auto-deleting all streams for class ${classInfo.class_name} (2-stream rule)`);
            }
          }
        }
      }
    }

    // Remove duplicates from streamsToDelete
    streamsToDelete = [...new Set(streamsToDelete)];

    console.log(`🔄 Deleting streams: ${streamsToDelete.join(', ')}`);
    console.log(`📝 Note: Database foreign keys will automatically handle student stream_id updates (ON DELETE SET NULL)`);

    // Delete all streams
    const deleteQuery = `DELETE FROM streams WHERE id IN (${streamsToDelete.map(() => '?').join(',')})`;
    const deleteResult = await executeQuery(deleteQuery, streamsToDelete);

    if (!deleteResult.success) {
      throw new Error(deleteResult.error);
    }

    // Prepare response message
    let message = `Stream "${stream.name}" deleted successfully.`;
    if (additionalStreamsDeleted.length > 0) {
      const additionalNames = additionalStreamsDeleted.map(s => s.name).join(', ');
      message += ` Additionally deleted streams: ${additionalNames} (2-stream rule applied).`;
    }
    message += ` Students were automatically unassigned from deleted streams and can be reassigned when new streams are created.`;

    res.json({
      success: true,
      message: message,
      data: {
        deleted_stream: stream,
        additional_streams_deleted: additionalStreamsDeleted,
        total_streams_deleted: streamsToDelete.length,
        business_rule_applied: additionalStreamsDeleted.length > 0,
        note: "Student stream assignments automatically updated via database foreign key constraints"
      }
    });

  } catch (error) {
    console.error('Delete stream error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete stream'
    });
  }
});

// Get streams for a specific class
router.get('/streams/class/:classId', async (req, res) => {
  try {
    const { classId } = req.params;

    console.log('🔍 Getting streams for class ID:', classId);

    // First get the class and its class_level_id
    const classQuery = `
      SELECT c.id, c.name, c.class_level_id,
             cl.code as class_level_code, cl.name as class_level_name, cl.streams_optional,
             el.code as education_level_code, el.name as education_level_name
      FROM classes c
      JOIN class_levels cl ON c.class_level_id = cl.id
      JOIN education_levels el ON cl.education_level_id = el.id
      WHERE c.id = ? AND c.is_active = TRUE
    `;

    const classResult = await executeQuery(classQuery, [classId]);

    if (!classResult.success || classResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Class not found'
      });
    }

    const classInfo = classResult.data[0];
    console.log('📚 Class info:', classInfo);

    // Get streams for this class level (permanent assignments)
    const streamsQuery = `
      SELECT DISTINCT s.id, s.name, s.stream_type
      FROM streams s
      JOIN stream_classes sc ON s.id = sc.stream_id
      WHERE sc.class_level_id = ?
      ORDER BY s.name
    `;

    const streamsResult = await executeQuery(streamsQuery, [classInfo.class_level_id]);
    let streams = streamsResult.success ? streamsResult.data : [];

    console.log('🔄 Found streams for class level', classInfo.class_level_code, ':', streams.length);

    // Special handling for A-Level classes: if no streams found, ensure A-Level streams are available
    if (classInfo.education_level_code === 'a_level' && streams.length === 0) {
      console.log('🔧 No A-Level streams found, attempting to create stream assignments...');

      // Try to create missing stream_classes entries for A-Level
      const createStreamAssignmentsQuery = `
        INSERT IGNORE INTO stream_classes (stream_id, class_level_id)
        SELECT s.id, cl.id
        FROM streams s
        CROSS JOIN class_levels cl
        JOIN education_levels el ON cl.education_level_id = el.id
        WHERE s.stream_type = 'a_level'
          AND el.code = 'a_level'
      `;

      await executeQuery(createStreamAssignmentsQuery);

      // Retry the streams query
      const retryStreamsResult = await executeQuery(streamsQuery, [classInfo.class_level_id]);
      streams = retryStreamsResult.success ? retryStreamsResult.data : [];

      console.log('🔄 After creating assignments, found streams:', streams.length);
    }

    // Build stream options based on education level and available streams
    const streamOptions = [];

    if (classInfo.education_level_code === 'o_level') {
      // Get O-Level streams for this class
      const oLevelStreams = streams.filter(s => s.stream_type === 'o_level');

      if (oLevelStreams.length > 0) {
        // If streams exist for this class, only show the actual streams (no default option)
        streamOptions.push(...oLevelStreams.map(stream => ({
          ...stream,
          is_default: false
        })));
      } else {
        // If no streams exist for this class, show "No Stream (Default)" option
        streamOptions.push({
          id: null,
          name: 'No Stream (Default)',
          stream_type: 'o_level',
          is_default: true
        });
      }
    } else if (classInfo.education_level_code === 'a_level') {
      // For A-Level: Only show Arts and Sciences streams (mandatory)
      streamOptions.push(...streams.filter(s => s.stream_type === 'a_level').map(stream => ({
        ...stream,
        is_default: false
      })));
    }

    // Determine if streams are required:
    // - A-Level classes always require streams
    // - O-Level classes require streams if they have streams available (not just the default option)
    const oLevelStreams = streams.filter(s => s.stream_type === 'o_level');
    const isStreamsRequired = classInfo.education_level_code === 'a_level' ||
                             (classInfo.education_level_code === 'o_level' && oLevelStreams.length > 0);
    const hasStreams = streamOptions.length > 0;

    console.log('✅ Stream options prepared:', {
      total: streamOptions.length,
      required: isStreamsRequired,
      hasStreams: hasStreams
    });

    res.json({
      success: true,
      data: {
        class_info: classInfo,
        streams: streamOptions,
        has_streams: hasStreams,
        streams_required: isStreamsRequired
      }
    });

  } catch (error) {
    console.error('Get streams for class error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve streams for class'
    });
  }
});

// =============================================
// CLASSES ROUTES
// =============================================

// Get classes with optional filters
router.get('/classes', async (req, res) => {
  try {
    const { level } = req.query;

    // Get base classes with their education level and class level info
    let query = `
      SELECT
        c.id, c.name, c.is_active, c.created_at, c.updated_at,
        cl.id as class_level_id, cl.code as class_level_code, cl.name as class_level_name,
        cl.display_name as class_level_display_name, cl.sort_order, cl.streams_optional,
        el.id as education_level_id, el.code as education_level_code, el.name as education_level_name,
        el.display_name as education_level_display_name
      FROM classes c
      JOIN class_levels cl ON c.class_level_id = cl.id
      JOIN education_levels el ON cl.education_level_id = el.id
      WHERE c.is_active = TRUE
    `;

    let params = [];

    if (level) {
      query += ' AND el.code = ?';
      params.push(level);
    }

    query += ' ORDER BY cl.sort_order, c.name';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    // For each class, get its available streams (if any)
    const classesWithStreams = await Promise.all(result.data.map(async (cls) => {
      const streamsQuery = `
        SELECT s.id, s.name, s.stream_type
        FROM streams s
        JOIN stream_classes sc ON s.id = sc.stream_id
        WHERE sc.class_level_id = ?
        ORDER BY s.name
      `;

      const streamsResult = await executeQuery(streamsQuery, [cls.class_level_id]);

      return {
        ...cls,
        streams: streamsResult.success ? streamsResult.data : [],
        has_streams: streamsResult.success && streamsResult.data.length > 0
      };
    }));

    res.json({
      success: true,
      data: classesWithStreams
    });

  } catch (error) {
    console.error('Get classes error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve classes'
    });
  }
});

// Create class
router.post('/classes', async (req, res) => {
  try {
    const { name, class_level_id } = req.body;

    // Validate required fields
    if (!name || !class_level_id) {
      return res.status(400).json({
        success: false,
        message: 'Name and class level are required'
      });
    }

    const insertQuery = `
      INSERT INTO classes (name, class_level_id, is_active, created_at, updated_at)
      VALUES (?, ?, TRUE, NOW(), NOW())
    `;

    const result = await executeQuery(insertQuery, [name, class_level_id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get the created class with related data
    const newClassQuery = `
      SELECT
        c.*, cl.name as class_level_name, el.name as education_level_name, s.name as stream_name
      FROM classes c
      JOIN class_levels cl ON c.class_level_id = cl.id
      JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN stream_classes sc ON c.class_level_id = sc.class_level_id
      LEFT JOIN streams s ON sc.stream_id = s.id
      WHERE c.id = ?
    `;
    const newClassResult = await executeQuery(newClassQuery, [result.data.insertId]);

    res.status(201).json({
      success: true,
      message: 'Class created successfully',
      data: newClassResult.data[0]
    });

  } catch (error) {
    console.error('Create class error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create class'
    });
  }
});

// Update class
router.put('/classes/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, class_level_id, is_active } = req.body;

    // Check if class exists
    const checkQuery = 'SELECT id FROM classes WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Class not found'
      });
    }

    const updateQuery = `
      UPDATE classes SET
        name = ?, class_level_id = ?, is_active = ?
      WHERE id = ?
    `;

    const result = await executeQuery(updateQuery, [name, class_level_id, is_active, id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get updated class
    const updatedClassQuery = `
      SELECT
        c.*, cl.name as class_level_name, el.name as education_level_name, s.name as stream_name
      FROM classes c
      JOIN class_levels cl ON c.class_level_id = cl.id
      JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN stream_classes sc ON c.class_level_id = sc.class_level_id
      LEFT JOIN streams s ON sc.stream_id = s.id
      WHERE c.id = ?
    `;
    const updatedClassResult = await executeQuery(updatedClassQuery, [id]);

    res.json({
      success: true,
      message: 'Class updated successfully',
      data: updatedClassResult.data[0]
    });

  } catch (error) {
    console.error('Update class error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update class'
    });
  }
});

// Delete class
router.delete('/classes/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if class exists
    const checkQuery = 'SELECT id FROM classes WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Class not found'
      });
    }

    // Delete class
    const deleteQuery = 'DELETE FROM classes WHERE id = ?';
    const deleteResult = await executeQuery(deleteQuery, [id]);

    if (!deleteResult.success) {
      throw new Error(deleteResult.error);
    }

    res.json({
      success: true,
      message: 'Class deleted successfully'
    });

  } catch (error) {
    console.error('Delete class error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete class'
    });
  }
});

// =============================================
// CURRENT CONTEXT ENDPOINT
// =============================================

// Get available years (generate programmatically since name is VARCHAR, not ENUM)
router.get('/year-options', async (req, res) => {
  try {
    // Generate years programmatically since academic_years.name is VARCHAR with 4-digit format
    const currentYear = new Date().getFullYear();
    const startYear = currentYear;     // Start with current year
    const endYear = currentYear + 4;   // Allow 4 years forward

    const availableYears = [];
    for (let year = startYear; year <= endYear; year++) {
      availableYears.push(year.toString());
    }

    res.json({
      success: true,
      data: availableYears,
      message: 'Available years generated successfully'
    });

  } catch (error) {
    console.error('Get year options error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate available years',
      error: error.message
    });
  }
});

// Get current academic context (active year and term)
router.get('/current-context', async (req, res) => {
  try {
    console.log('🔍 Getting current academic context...');

    // Get active academic year (using 1 instead of TRUE for MySQL compatibility)
    const yearQuery = `
      SELECT id, name, start_date, end_date, is_active
      FROM academic_years
      WHERE is_active = 1
      LIMIT 1
    `;

    const yearResult = await executeQuery(yearQuery);
    console.log('📅 Academic year query result:', yearResult);

    // Get active term (using 1 instead of TRUE for MySQL compatibility)
    const termQuery = `
      SELECT id, name, number, academic_year_id, start_date, end_date, is_active
      FROM terms
      WHERE is_active = 1
      LIMIT 1
    `;

    const termResult = await executeQuery(termQuery);
    console.log('📆 Term query result:', termResult);

    const context = {
      academicYear: yearResult.success && yearResult.data.length > 0 ? yearResult.data[0] : null,
      currentTerm: termResult.success && termResult.data.length > 0 ? termResult.data[0] : null,
      setupRequired: (!yearResult.success || yearResult.data.length === 0) || (!termResult.success || termResult.data.length === 0)
    };

    console.log('📊 Final context:', context);

    res.json({
      success: true,
      data: context
    });

  } catch (error) {
    console.error('Get current context error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve current academic context',
      error: error.message
    });
  }
});

// =============================================
// GRADING SCALE ROUTES
// =============================================

// Get O-Level grading scale
router.get('/grading-scale/o-level', async (req, res) => {
  try {
    const query = `
      SELECT
        id, competency_level, competency_description,
        min_score, max_score, is_active, created_at, updated_at
      FROM o_level_grading_scale
      WHERE is_active = TRUE
      ORDER BY competency_level ASC
    `;

    const result = await executeQuery(query);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data,
      message: 'O-Level grading scale retrieved successfully'
    });

  } catch (error) {
    console.error('Get O-Level grading scale error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve O-Level grading scale',
      error: error.message
    });
  }
});

// Get specific O-Level grading scale entry
router.get('/grading-scale/o-level/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT
        id, competency_level, competency_description,
        min_score, max_score, created_at, updated_at
      FROM o_level_grading_scale
      WHERE id = ?
    `;

    const result = await executeQuery(query, [id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Grading scale entry not found'
      });
    }

    res.json({
      success: true,
      data: result.data[0],
      message: 'Grading scale entry retrieved successfully'
    });

  } catch (error) {
    console.error('Get grading scale entry error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve grading scale entry',
      error: error.message
    });
  }
});

// Update O-Level grading scale entry
router.put('/grading-scale/o-level/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { competency_description, min_score, max_score } = req.body;

    const query = `
      UPDATE o_level_grading_scale
      SET
        competency_description = ?,
        min_score = ?,
        max_score = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;

    const result = await executeQuery(query, [
      competency_description, min_score, max_score, id
    ]);

    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'Grading scale entry not found'
      });
    }

    res.json({
      success: true,
      message: 'Grading scale entry updated successfully'
    });

  } catch (error) {
    console.error('Update grading scale entry error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update grading scale entry',
      error: error.message
    });
  }
});

// =============================================
// GRADE BOUNDARIES ROUTES
// =============================================

// Get O-Level grade boundaries
router.get('/grade-boundaries/o-level', async (req, res) => {
  try {
    const query = `
      SELECT
        id, grade_letter, min_percentage, max_percentage,
        grade_descriptor, created_at, updated_at
      FROM o_level_grade_boundaries
      ORDER BY min_percentage DESC
    `;

    const result = await executeQuery(query);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data,
      message: 'O-Level grade boundaries retrieved successfully'
    });

  } catch (error) {
    console.error('Get O-Level grade boundaries error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve O-Level grade boundaries',
      error: error.message
    });
  }
});

// Create O-Level grade boundary
router.post('/grade-boundaries/o-level', authenticateToken, async (req, res) => {
  try {
    const { grade_letter, min_percentage, max_percentage, grade_descriptor } = req.body;

    // Validate required fields
    if (!grade_letter || min_percentage === undefined || max_percentage === undefined || !grade_descriptor) {
      return res.status(400).json({
        success: false,
        message: 'Grade letter, percentages, and descriptor are required'
      });
    }

    // Check if grade already exists
    const checkQuery = 'SELECT id FROM o_level_grade_boundaries WHERE grade_letter = ?';
    const checkResult = await executeQuery(checkQuery, [grade_letter]);

    if (checkResult.success && checkResult.data.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Grade boundary already exists'
      });
    }

    const insertQuery = `
      INSERT INTO o_level_grade_boundaries
      (grade_letter, min_percentage, max_percentage, grade_descriptor)
      VALUES (?, ?, ?, ?)
    `;

    const result = await executeQuery(insertQuery, [
      grade_letter, min_percentage, max_percentage, grade_descriptor
    ]);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.status(201).json({
      success: true,
      message: 'O-Level grade boundary created successfully',
      data: { id: result.data.insertId }
    });

  } catch (error) {
    console.error('Create O-Level grade boundary error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create O-Level grade boundary',
      error: error.message
    });
  }
});

// Update O-Level grade boundaries
router.put('/grade-boundaries/o-level', authenticateToken, async (req, res) => {
  try {
    const { boundaries } = req.body;

    if (!boundaries || !Array.isArray(boundaries)) {
      return res.status(400).json({
        success: false,
        message: 'Boundaries array is required'
      });
    }

    // Process each boundary (update existing or create new)
    for (const boundary of boundaries) {
      const { grade_letter, min_percentage, max_percentage, grade_descriptor } = boundary;

      // Check if boundary exists
      const checkQuery = 'SELECT id FROM o_level_grade_boundaries WHERE grade_letter = ?';
      const checkResult = await executeQuery(checkQuery, [grade_letter]);

      if (checkResult.success && checkResult.data.length > 0) {
        // Update existing boundary
        const updateQuery = `
          UPDATE o_level_grade_boundaries
          SET min_percentage = ?, max_percentage = ?, grade_descriptor = ?, updated_at = CURRENT_TIMESTAMP
          WHERE grade_letter = ?
        `;

        const result = await executeQuery(updateQuery, [
          min_percentage, max_percentage, grade_descriptor, grade_letter
        ]);

        if (!result.success) {
          throw new Error(`Failed to update grade ${grade_letter}: ${result.error}`);
        }
      } else {
        // Create new boundary
        const insertQuery = `
          INSERT INTO o_level_grade_boundaries
          (grade_letter, min_percentage, max_percentage, grade_descriptor)
          VALUES (?, ?, ?, ?)
        `;

        const result = await executeQuery(insertQuery, [
          grade_letter, min_percentage, max_percentage, grade_descriptor
        ]);

        if (!result.success) {
          throw new Error(`Failed to create grade ${grade_letter}: ${result.error}`);
        }
      }
    }

    res.json({
      success: true,
      message: 'O-Level grade boundaries updated successfully'
    });

  } catch (error) {
    console.error('Update O-Level grade boundaries error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update O-Level grade boundaries',
      error: error.message
    });
  }
});

// Delete O-Level grade boundary
router.delete('/grade-boundaries/o-level/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if boundary exists
    const checkQuery = 'SELECT id FROM o_level_grade_boundaries WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Grade boundary not found'
      });
    }

    // Hard delete since is_active column no longer exists
    const deleteQuery = 'DELETE FROM o_level_grade_boundaries WHERE id = ?';
    const result = await executeQuery(deleteQuery, [id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      message: 'O-Level grade boundary deleted successfully'
    });

  } catch (error) {
    console.error('Delete O-Level grade boundary error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete O-Level grade boundary',
      error: error.message
    });
  }
});

// Get O-Level grade boundaries history (placeholder - no history table exists)
router.get('/grade-boundaries/o-level/history', async (req, res) => {
  try {
    // Since there's no history table, return recent updates from the main table
    const query = `
      SELECT
        id, grade_letter, min_percentage, max_percentage, grade_descriptor,
        updated_at as change_date, 'Updated' as change_type
      FROM o_level_grade_boundaries
      WHERE is_active = TRUE
      ORDER BY updated_at DESC
      LIMIT 10
    `;

    const result = await executeQuery(query);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data,
      message: 'O-Level grade boundaries history retrieved successfully'
    });

  } catch (error) {
    console.error('Get O-Level grade boundaries history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve O-Level grade boundaries history',
      error: error.message
    });
  }
});

// NOTE: Old A-Level Principal grade boundaries routes removed - using new paper-based system





// NOTE: Old A-Level grade boundary routes removed - using new paper-based system

// =============================================
// EXAM TYPES ROUTES
// =============================================

// Get all exam types
router.get('/exam-types', async (req, res) => {
  try {
    const query = `
      SELECT id, name, short_name, is_active, sort_order, created_at, updated_at
      FROM exam_types
      WHERE is_active = TRUE
      ORDER BY sort_order, name
    `;

    const result = await executeQuery(query);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get exam types error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve exam types'
    });
  }
});

// Create new exam type
router.post('/exam-types', async (req, res) => {
  try {
    const { name, short_name, sort_order } = req.body;

    // Validate required fields
    if (!name || !short_name) {
      return res.status(400).json({
        success: false,
        message: 'Name and short name are required'
      });
    }

    // Check for duplicate names
    const duplicateCheck = await executeQuery(
      'SELECT id FROM exam_types WHERE name = ? OR short_name = ?',
      [name, short_name]
    );

    if (duplicateCheck.success && duplicateCheck.data.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Exam type with this name or short name already exists'
      });
    }

    // Insert new exam type
    const insertQuery = `
      INSERT INTO exam_types (name, short_name, sort_order, is_active)
      VALUES (?, ?, ?, TRUE)
    `;

    const result = await executeQuery(insertQuery, [
      name, short_name, sort_order || 99
    ]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get the created exam type
    const newExamTypeQuery = 'SELECT * FROM exam_types WHERE id = ?';
    const newExamTypeResult = await executeQuery(newExamTypeQuery, [result.data.insertId]);

    res.status(201).json({
      success: true,
      message: 'Exam type created successfully',
      data: newExamTypeResult.data[0]
    });

  } catch (error) {
    console.error('Create exam type error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create exam type'
    });
  }
});

// Update exam type
router.put('/exam-types/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, short_name, sort_order, is_active } = req.body;

    // Check if exam type exists
    const checkQuery = 'SELECT id FROM exam_types WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Exam type not found'
      });
    }

    // Update exam type
    const updateQuery = `
      UPDATE exam_types
      SET name = ?, short_name = ?, sort_order = ?, is_active = ?
      WHERE id = ?
    `;

    const updateResult = await executeQuery(updateQuery, [
      name, short_name, sort_order, is_active, id
    ]);

    if (!updateResult.success) {
      throw new Error(updateResult.error);
    }

    // Get updated exam type
    const updatedExamTypeQuery = 'SELECT * FROM exam_types WHERE id = ?';
    const updatedExamTypeResult = await executeQuery(updatedExamTypeQuery, [id]);

    res.json({
      success: true,
      message: 'Exam type updated successfully',
      data: updatedExamTypeResult.data[0]
    });

  } catch (error) {
    console.error('Update exam type error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update exam type'
    });
  }
});

// =============================================
// ACADEMIC CONTEXT MANAGEMENT ROUTES
// =============================================

// Set active academic year
router.patch('/years/:id/set-active', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if academic year exists
    const checkQuery = 'SELECT id, name FROM academic_years WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Academic year not found'
      });
    }

    // Deactivate all academic years first
    const deactivateQuery = 'UPDATE academic_years SET is_active = FALSE';
    const deactivateResult = await executeQuery(deactivateQuery);

    if (!deactivateResult.success) {
      throw new Error('Failed to deactivate existing academic years');
    }

    // Activate the selected academic year
    const activateQuery = 'UPDATE academic_years SET is_active = TRUE WHERE id = ?';
    const activateResult = await executeQuery(activateQuery, [id]);

    if (!activateResult.success) {
      throw new Error('Failed to activate academic year');
    }

    res.json({
      success: true,
      message: `Academic year ${checkResult.data[0].name} set as active`,
      data: { id: parseInt(id), name: checkResult.data[0].name }
    });

  } catch (error) {
    console.error('Set active academic year error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to set active academic year'
    });
  }
});

// Set active term
router.patch('/terms/:id/set-active', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if term exists
    const checkQuery = 'SELECT id, name, academic_year_id FROM terms WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Term not found'
      });
    }

    const term = checkResult.data[0];

    // Deactivate all terms first
    const deactivateQuery = 'UPDATE terms SET is_active = FALSE';
    const deactivateResult = await executeQuery(deactivateQuery);

    if (!deactivateResult.success) {
      throw new Error('Failed to deactivate existing terms');
    }

    // Activate the selected term
    const activateQuery = 'UPDATE terms SET is_active = TRUE WHERE id = ?';
    const activateResult = await executeQuery(activateQuery, [id]);

    if (!activateResult.success) {
      throw new Error('Failed to activate term');
    }

    res.json({
      success: true,
      message: `${term.name} set as active term`,
      data: { id: parseInt(id), name: term.name, academic_year_id: term.academic_year_id }
    });

  } catch (error) {
    console.error('Set active term error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to set active term'
    });
  }
});

// Get terms by academic year
router.get('/terms/year/:academicYearId', async (req, res) => {
  try {
    const { academicYearId } = req.params;

    const query = `
      SELECT t.id, t.name, t.number, t.start_date, t.end_date, t.is_active,
             t.academic_year_id, t.created_at, t.updated_at,
             ay.name as academic_year_name
      FROM terms t
      JOIN academic_years ay ON t.academic_year_id = ay.id
      WHERE t.academic_year_id = ?
      ORDER BY t.number
    `;

    const result = await executeQuery(query, [academicYearId]);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get terms by academic year error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve terms for academic year'
    });
  }
});

// =============================================
// ACADEMIC CONTEXT MANAGEMENT ROUTES
// =============================================

// Set active academic year
router.patch('/years/:id/set-active', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if academic year exists
    const checkQuery = 'SELECT id, name FROM academic_years WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Academic year not found'
      });
    }

    // Deactivate all academic years first
    const deactivateQuery = 'UPDATE academic_years SET is_active = FALSE';
    const deactivateResult = await executeQuery(deactivateQuery);

    if (!deactivateResult.success) {
      throw new Error('Failed to deactivate existing academic years');
    }

    // Activate the selected academic year
    const activateQuery = 'UPDATE academic_years SET is_active = TRUE WHERE id = ?';
    const activateResult = await executeQuery(activateQuery, [id]);

    if (!activateResult.success) {
      throw new Error('Failed to activate academic year');
    }

    res.json({
      success: true,
      message: `Academic year ${checkResult.data[0].name} set as active`,
      data: { id: parseInt(id), name: checkResult.data[0].name }
    });

  } catch (error) {
    console.error('Set active academic year error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to set active academic year'
    });
  }
});

// Set active term
router.patch('/terms/:id/set-active', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if term exists
    const checkQuery = 'SELECT id, name, academic_year_id FROM terms WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Term not found'
      });
    }

    const term = checkResult.data[0];

    // Deactivate all terms first
    const deactivateQuery = 'UPDATE terms SET is_active = FALSE';
    const deactivateResult = await executeQuery(deactivateQuery);

    if (!deactivateResult.success) {
      throw new Error('Failed to deactivate existing terms');
    }

    // Activate the selected term
    const activateQuery = 'UPDATE terms SET is_active = TRUE WHERE id = ?';
    const activateResult = await executeQuery(activateQuery, [id]);

    if (!activateResult.success) {
      throw new Error('Failed to activate term');
    }

    res.json({
      success: true,
      message: `${term.name} set as active term`,
      data: { id: parseInt(id), name: term.name, academic_year_id: term.academic_year_id }
    });

  } catch (error) {
    console.error('Set active term error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to set active term'
    });
  }
});

// Get terms by academic year
router.get('/terms/year/:academicYearId', async (req, res) => {
  try {
    const { academicYearId } = req.params;

    const query = `
      SELECT t.id, t.name, t.number, t.start_date, t.end_date, t.is_active,
             t.academic_year_id, t.created_at, t.updated_at,
             ay.name as academic_year_name
      FROM terms t
      JOIN academic_years ay ON t.academic_year_id = ay.id
      WHERE t.academic_year_id = ?
      ORDER BY t.number
    `;

    const result = await executeQuery(query, [academicYearId]);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get terms by academic year error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve terms for academic year'
    });
  }
});

// =============================================
// STREAM ASSIGNMENT MANAGEMENT ROUTES
// =============================================

// Get all stream assignments (permanent assignments)
router.get('/streams/assignments', async (req, res) => {
  try {
    const { class_level_id, stream_type } = req.query;

    console.log('📋 Getting stream assignments:', { class_level_id, stream_type });

    let whereClause = 'WHERE 1=1';
    let params = [];

    if (class_level_id) {
      whereClause += ' AND sc.class_level_id = ?';
      params.push(class_level_id);
    }

    if (stream_type) {
      whereClause += ' AND s.stream_type = ?';
      params.push(stream_type);
    }

    const query = `
      SELECT
        sc.id as assignment_id,
        sc.stream_id,
        s.name as stream_name,
        s.stream_type,
        sc.class_level_id,
        cl.name as class_level_name,
        cl.code as class_level_code,
        sc.created_at,
        sc.updated_at,
        el.code as education_level_code,
        el.name as education_level_name
      FROM stream_classes sc
      JOIN streams s ON sc.stream_id = s.id
      JOIN class_levels cl ON sc.class_level_id = cl.id
      JOIN education_levels el ON cl.education_level_id = el.id
      ${whereClause}
      ORDER BY cl.sort_order ASC, s.name ASC
    `;

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('❌ Get stream assignments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve stream assignments',
      error: error.message
    });
  }
});

// Assign stream to class (permanent assignment)
router.post('/streams/assign', async (req, res) => {
  try {
    const { stream_id, class_level_id } = req.body;

    console.log('📝 Assigning stream to class:', { stream_id, class_level_id });

    // Validate required fields
    if (!stream_id || !class_level_id) {
      return res.status(400).json({
        success: false,
        message: 'Stream ID and class level ID are required'
      });
    }

    // Check if stream exists
    const streamQuery = 'SELECT id, name, stream_type FROM streams WHERE id = ?';
    const streamResult = await executeQuery(streamQuery, [stream_id]);

    if (!streamResult.success || streamResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Stream not found'
      });
    }

    const stream = streamResult.data[0];

    // Check if class level exists
    const classLevelQuery = 'SELECT id, name, code FROM class_levels WHERE id = ? AND is_active = TRUE';
    const classLevelResult = await executeQuery(classLevelQuery, [class_level_id]);

    if (!classLevelResult.success || classLevelResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Class level not found or inactive'
      });
    }

    // Check if assignment already exists
    const existingQuery = `
      SELECT id FROM stream_classes
      WHERE stream_id = ? AND class_level_id = ?
    `;
    const existingResult = await executeQuery(existingQuery, [stream_id, class_level_id]);

    if (existingResult.success && existingResult.data.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Stream is already assigned to this class'
      });
    }

    // Additional validation: Check if there are students currently enrolled without streams
    // who would need to be assigned to streams when adding the first stream
    const studentsWithoutStreamQuery = `
      SELECT COUNT(*) as student_count
      FROM o_level_students s
      JOIN classes c ON s.current_class_id = c.id
      WHERE c.class_level_id = ?
        AND s.stream_id IS NULL
        AND s.status = 'active'
    `;

    const studentsWithoutStreamResult = await executeQuery(studentsWithoutStreamQuery, [class_level_id]);

    if (!studentsWithoutStreamResult.success) {
      throw new Error('Failed to check for students without streams');
    }

    const studentsWithoutStream = studentsWithoutStreamResult.data[0].student_count;

    // Check how many streams currently exist for this class
    const currentStreamsQuery = `
      SELECT COUNT(*) as current_count
      FROM stream_classes sc
      JOIN streams s ON sc.stream_id = s.id
      WHERE sc.class_level_id = ?
        AND s.stream_type = 'o_level'
    `;

    const currentStreamsResult = await executeQuery(currentStreamsQuery, [class_level_id]);

    if (!currentStreamsResult.success) {
      throw new Error('Failed to check current streams');
    }

    const currentStreamCount = currentStreamsResult.data[0].current_count;

    // For O-Level classes, enforce the rule: either 0 streams (whole class) or 2+ streams (subdivided)
    if (stream.stream_type === 'o_level') {
      if (currentStreamCount === 0) {
        // This would be the first stream - not allowed for O-Level
        return res.status(400).json({
          success: false,
          message: 'O-Level classes cannot have just one stream. Please use the bulk assignment endpoint to assign at least 2 streams simultaneously to subdivide the class properly.',
          data: {
            current_streams: currentStreamCount,
            students_without_stream: studentsWithoutStream,
            suggestion: 'Use POST /academic/streams/assign-multiple with an array of stream_ids to create multiple streams at once',
            endpoint_recommendation: '/academic/streams/assign-multiple'
          }
        });
      }
    }

    // If this is adding to existing streams and there are students without streams,
    // provide a warning but allow the operation
    let warningMessage = '';
    if (currentStreamCount > 0 && studentsWithoutStream > 0) {
      warningMessage = ` Note: There are ${studentsWithoutStream} students currently enrolled without streams who will need to be assigned to streams.`;
    }

    // Create the assignment
    const insertQuery = `
      INSERT INTO stream_classes (stream_id, class_level_id)
      VALUES (?, ?)
    `;
    const insertResult = await executeQuery(insertQuery, [stream_id, class_level_id]);

    if (!insertResult.success) {
      throw new Error('Failed to create stream assignment');
    }

    res.status(201).json({
      success: true,
      message: `Stream assigned to class successfully.${warningMessage}`,
      data: {
        id: insertResult.insertId,
        stream_id,
        class_level_id,
        students_without_stream: studentsWithoutStream,
        is_first_stream: currentStreamCount === 0
      }
    });

  } catch (error) {
    console.error('❌ Stream assignment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to assign stream to class',
      error: error.message
    });
  }
});

// Bulk assign multiple streams to class (permanent assignments)
router.post('/streams/assign-multiple', async (req, res) => {
  try {
    const { stream_ids, class_level_id } = req.body;

    console.log('📝 Bulk assigning streams to class:', { stream_count: stream_ids?.length, class_level_id });

    // Validate required fields
    if (!stream_ids || !Array.isArray(stream_ids) || stream_ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Stream IDs array is required and cannot be empty'
      });
    }

    if (!class_level_id) {
      return res.status(400).json({
        success: false,
        message: 'Class level ID is required'
      });
    }

    // For O-Level classes, enforce minimum 2 streams rule
    if (stream_ids.length === 1) {
      // Check if this is an O-Level stream
      const streamQuery = 'SELECT stream_type FROM streams WHERE id = ?';
      const streamResult = await executeQuery(streamQuery, [stream_ids[0]]);

      if (streamResult.success && streamResult.data.length > 0 && streamResult.data[0].stream_type === 'o_level') {
        return res.status(400).json({
          success: false,
          message: 'O-Level classes cannot have just one stream. Please assign at least 2 streams to subdivide the class properly.',
          data: {
            provided_streams: stream_ids.length,
            minimum_required: 2
          }
        });
      }
    }

    // Validate all streams exist
    const streamPlaceholders = stream_ids.map(() => '?').join(',');
    const streamsQuery = `SELECT id, name, stream_type FROM streams WHERE id IN (${streamPlaceholders})`;
    const streamsResult = await executeQuery(streamsQuery, stream_ids);

    if (!streamsResult.success || streamsResult.data.length !== stream_ids.length) {
      return res.status(404).json({
        success: false,
        message: 'One or more streams not found'
      });
    }

    const streams = streamsResult.data;

    // Check if class level exists
    const classLevelQuery = 'SELECT id, name, code FROM class_levels WHERE id = ? AND is_active = TRUE';
    const classLevelResult = await executeQuery(classLevelQuery, [class_level_id]);

    if (!classLevelResult.success || classLevelResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Class level not found or inactive'
      });
    }

    // Check for existing assignments
    const existingQuery = `
      SELECT stream_id FROM stream_classes
      WHERE stream_id IN (${streamPlaceholders}) AND class_level_id = ?
    `;
    const existingParams = [...stream_ids, class_level_id];
    const existingResult = await executeQuery(existingQuery, existingParams);

    if (existingResult.success && existingResult.data.length > 0) {
      const existingStreamIds = existingResult.data.map(row => row.stream_id);
      const existingStreamNames = streams.filter(s => existingStreamIds.includes(s.id)).map(s => s.name);

      return res.status(409).json({
        success: false,
        message: `Some streams are already assigned to this class: ${existingStreamNames.join(', ')}`
      });
    }

    // Create all assignments
    const insertValues = stream_ids.map(streamId => `(${streamId}, ${class_level_id})`).join(',');
    const insertQuery = `
      INSERT INTO stream_classes (stream_id, class_level_id)
      VALUES ${insertValues}
    `;

    const insertResult = await executeQuery(insertQuery, []);

    if (!insertResult.success) {
      throw new Error('Failed to create stream assignments');
    }

    res.status(201).json({
      success: true,
      message: `${stream_ids.length} streams assigned to class successfully. Class is now subdivided into streams.`,
      data: {
        assigned_streams: streams,
        class_level_id,
        assignments_created: stream_ids.length
      }
    });

  } catch (error) {
    console.error('❌ Bulk stream assignment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to assign streams to class',
      error: error.message
    });
  }
});

// Remove stream assignment from class for specific academic period
router.delete('/streams/assign/:assignmentId', async (req, res) => {
  try {
    const { assignmentId } = req.params;

    console.log('🗑️ Removing stream assignment:', assignmentId);

    // Check if assignment exists
    const checkQuery = `
      SELECT sc.*, s.name as stream_name, cl.name as class_level_name
      FROM stream_classes sc
      JOIN streams s ON sc.stream_id = s.id
      JOIN class_levels cl ON sc.class_level_id = cl.id
      WHERE sc.id = ?
    `;
    const checkResult = await executeQuery(checkQuery, [assignmentId]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Stream assignment not found'
      });
    }

    const assignment = checkResult.data[0];

    // Don't allow removal of A-Level stream assignments (they are permanent)
    if (assignment.stream_type === 'a_level') {
      return res.status(403).json({
        success: false,
        message: 'A-Level stream assignments cannot be removed as they are permanent'
      });
    }

    // Note: With permanent streams, we allow stream deletion regardless of student enrollment
    // Students' stream_id will be set to NULL automatically via foreign key constraints

    // Check how many streams will remain for this class after removal
    const remainingStreamsQuery = `
      SELECT COUNT(*) as remaining_count
      FROM stream_classes sc
      JOIN streams s ON sc.stream_id = s.id
      WHERE sc.class_level_id = ?
        AND sc.id != ?
        AND s.stream_type = 'o_level'
    `;

    const remainingResult = await executeQuery(remainingStreamsQuery, [
      assignment.class_level_id,
      assignmentId
    ]);

    if (!remainingResult.success) {
      throw new Error('Failed to check remaining streams');
    }

    const remainingCount = remainingResult.data[0].remaining_count;

    // Remove the assignment
    const deleteQuery = 'DELETE FROM stream_classes WHERE id = ?';
    const deleteResult = await executeQuery(deleteQuery, [assignmentId]);

    if (!deleteResult.success) {
      throw new Error('Failed to remove stream assignment');
    }

    let consolidationMessage = '';
    let autoConsolidated = false;

    // For O-Level classes: If only 1 stream remains, automatically consolidate to whole class
    if (assignment.stream_type === 'o_level' && remainingCount === 1) {
      console.log('🔄 Auto-consolidating class with single remaining stream');

      // Get the remaining stream
      const remainingStreamQuery = `
        SELECT sc.id, sc.stream_id, s.name as stream_name
        FROM stream_classes sc
        JOIN streams s ON sc.stream_id = s.id
        WHERE sc.class_level_id = ?
          AND s.stream_type = 'o_level'
      `;

      const remainingStreamResult = await executeQuery(remainingStreamQuery, [
        assignment.class_level_id
      ]);

      if (remainingStreamResult.success && remainingStreamResult.data.length === 1) {
        const remainingStream = remainingStreamResult.data[0];

        // Transfer all students from the remaining stream to no stream (whole class)
        const transferStudentsQuery = `
          UPDATE o_level_students
          SET stream_id = NULL, updated_at = CURRENT_TIMESTAMP
          WHERE stream_id = ?
            AND status = 'active'
        `;

        const transferResult = await executeQuery(transferStudentsQuery, [
          remainingStream.stream_id
        ]);

        if (transferResult.success) {
          // Remove the remaining stream assignment
          const removeRemainingQuery = 'DELETE FROM stream_classes WHERE id = ?';
          const removeRemainingResult = await executeQuery(removeRemainingQuery, [remainingStream.id]);

          if (removeRemainingResult.success) {
            autoConsolidated = true;
            consolidationMessage = ` The class had only one stream remaining ("${remainingStream.stream_name}"), so it was automatically consolidated to a whole class configuration. ${transferResult.affectedRows} students were moved to the whole class.`;
          }
        }
      }
    } else if (remainingCount === 0) {
      consolidationMessage = ' The class now has no streams and operates as a whole class.';
    }

    res.json({
      success: true,
      message: `Stream assignment removed successfully.${consolidationMessage}`,
      data: {
        ...assignment,
        remaining_streams: autoConsolidated ? 0 : remainingCount,
        auto_consolidated: autoConsolidated,
        final_configuration: autoConsolidated ? 'whole_class' : (remainingCount === 0 ? 'whole_class' : 'subdivided')
      }
    });

  } catch (error) {
    console.error('❌ Stream assignment removal error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove stream assignment',
      error: error.message
    });
  }
});

// Get students enrolled in a specific stream for transfer purposes
router.get('/streams/:streamId/students', async (req, res) => {
  try {
    const { streamId } = req.params;
    const { academic_year_id, term_id, class_level_id } = req.query;

    console.log('👥 Getting students in stream:', { streamId, academic_year_id, term_id, class_level_id });

    // Validate required parameters
    if (!academic_year_id || !term_id) {
      return res.status(400).json({
        success: false,
        message: 'Academic year ID and term ID are required'
      });
    }

    // Get stream information
    const streamQuery = 'SELECT id, name, stream_type FROM streams WHERE id = ?';
    const streamResult = await executeQuery(streamQuery, [streamId]);

    if (!streamResult.success || streamResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Stream not found'
      });
    }

    const stream = streamResult.data[0];

    // Build query based on stream type
    let studentsQuery = '';
    let params = [streamId, academic_year_id, term_id];

    if (stream.stream_type === 'o_level') {
      studentsQuery = `
        SELECT
          s.id, s.admission_number, s.first_name, s.middle_name, s.last_name,
          s.gender, s.status, s.enrollment_date,
          c.name as class_name, cl.name as class_level_name,
          ay.name as academic_year_name, t.name as term_name
        FROM o_level_students s
        JOIN classes c ON s.current_class_id = c.id
        JOIN class_levels cl ON c.class_level_id = cl.id
        JOIN academic_years ay ON s.current_academic_year_id = ay.id
        JOIN terms t ON s.current_term_id = t.id
        WHERE s.stream_id = ?
          AND s.current_academic_year_id = ?
          AND s.current_term_id = ?
          AND s.status = 'active'
      `;

      if (class_level_id) {
        studentsQuery += ' AND cl.id = ?';
        params.push(class_level_id);
      }

      studentsQuery += ' ORDER BY s.admission_number';
    } else {
      // A-Level students
      studentsQuery = `
        SELECT
          s.id, s.admission_number, s.first_name, s.middle_name, s.last_name,
          s.gender, s.status, s.registration_date as enrollment_date,
          c.name as class_name, cl.name as class_level_name,
          ay.name as academic_year_name, t.name as term_name
        FROM a_level_students s
        JOIN classes c ON s.current_class_id = c.id
        JOIN class_levels cl ON c.class_level_id = cl.id
        JOIN academic_years ay ON s.current_academic_year_id = ay.id
        JOIN terms t ON s.current_term_id = t.id
        WHERE s.stream_id = ?
          AND s.current_academic_year_id = ?
          AND s.current_term_id = ?
          AND s.status = 'active'
      `;

      if (class_level_id) {
        studentsQuery += ' AND cl.id = ?';
        params.push(class_level_id);
      }

      studentsQuery += ' ORDER BY s.admission_number';
    }

    const studentsResult = await executeQuery(studentsQuery, params);

    if (!studentsResult.success) {
      throw new Error('Failed to retrieve students');
    }

    res.json({
      success: true,
      data: {
        stream: stream,
        students: studentsResult.data,
        total_students: studentsResult.data.length
      }
    });

  } catch (error) {
    console.error('❌ Get stream students error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve students in stream',
      error: error.message
    });
  }
});

// Transfer students between streams
router.post('/streams/transfer-students', async (req, res) => {
  try {
    const { student_ids, from_stream_id, to_stream_id, academic_year_id, term_id } = req.body;

    console.log('🔄 Transferring students between streams:', {
      student_count: student_ids?.length,
      from_stream_id,
      to_stream_id,
      academic_year_id,
      term_id
    });

    // Validate required fields
    if (!student_ids || !Array.isArray(student_ids) || student_ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Student IDs array is required and cannot be empty'
      });
    }

    if (!from_stream_id || !academic_year_id || !term_id) {
      return res.status(400).json({
        success: false,
        message: 'From stream ID, academic year ID, and term ID are required'
      });
    }

    // Validate streams exist
    const fromStreamQuery = 'SELECT id, name, stream_type FROM streams WHERE id = ?';
    const fromStreamResult = await executeQuery(fromStreamQuery, [from_stream_id]);

    if (!fromStreamResult.success || fromStreamResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Source stream not found'
      });
    }

    const fromStream = fromStreamResult.data[0];

    // If to_stream_id is null, it means transferring to "no stream" (whole class)
    let toStream = null;
    if (to_stream_id) {
      const toStreamQuery = 'SELECT id, name, stream_type FROM streams WHERE id = ?';
      const toStreamResult = await executeQuery(toStreamQuery, [to_stream_id]);

      if (!toStreamResult.success || toStreamResult.data.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Target stream not found'
        });
      }

      toStream = toStreamResult.data[0];

      // Ensure both streams are of the same type
      if (fromStream.stream_type !== toStream.stream_type) {
        return res.status(400).json({
          success: false,
          message: 'Cannot transfer students between different stream types (O-Level vs A-Level)'
        });
      }
    }

    // Determine which table to update based on stream type
    const tableName = fromStream.stream_type === 'o_level' ? 'o_level_students' : 'a_level_students';

    // Build the update query
    const placeholders = student_ids.map(() => '?').join(',');
    const updateQuery = `
      UPDATE ${tableName}
      SET stream_id = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id IN (${placeholders})
        AND stream_id = ?
        AND current_academic_year_id = ?
        AND current_term_id = ?
        AND status = 'active'
    `;

    const updateParams = [
      to_stream_id, // null if transferring to whole class
      ...student_ids,
      from_stream_id,
      academic_year_id,
      term_id
    ];

    const updateResult = await executeQuery(updateQuery, updateParams);

    if (!updateResult.success) {
      throw new Error('Failed to transfer students');
    }

    const transferredCount = updateResult.affectedRows;

    if (transferredCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'No students were transferred. Please verify the students are currently enrolled in the source stream.'
      });
    }

    const transferMessage = to_stream_id
      ? `${transferredCount} students transferred from "${fromStream.name}" to "${toStream.name}"`
      : `${transferredCount} students transferred from "${fromStream.name}" to whole class (no stream)`;

    res.json({
      success: true,
      message: transferMessage,
      data: {
        transferred_count: transferredCount,
        from_stream: fromStream,
        to_stream: toStream,
        student_ids: student_ids
      }
    });

  } catch (error) {
    console.error('❌ Student transfer error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to transfer students between streams',
      error: error.message
    });
  }
});

// Note: Manual consolidation is no longer needed as it happens automatically
// when streams are reduced to 1 during the removal process

// =============================================
// CLASS-EXAM TYPE ASSOCIATIONS ROUTES
// =============================================

// Get class-exam type associations
router.get('/class-exam-types', async (req, res) => {
  try {
    const { academic_year_id, term_id, class_id } = req.query;

    let query = `
      SELECT
        cet.*,
        c.name as class_name,
        et.name as exam_type_name,
        et.short_name as exam_type_short_name,
        cet.weight_percentage,
        ay.name as academic_year_name,
        t.name as term_name
      FROM class_exam_types cet
      JOIN classes c ON cet.class_id = c.id
      JOIN exam_types et ON cet.exam_type_id = et.id
      JOIN academic_years ay ON cet.academic_year_id = ay.id
      JOIN terms t ON cet.term_id = t.id
      WHERE 1=1
    `;

    const params = [];

    if (academic_year_id) {
      query += ' AND cet.academic_year_id = ?';
      params.push(academic_year_id);
    }

    if (term_id) {
      query += ' AND cet.term_id = ?';
      params.push(term_id);
    }

    if (class_id) {
      query += ' AND cet.class_id = ?';
      params.push(class_id);
    }

    query += ' ORDER BY c.name, et.sort_order, et.name';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get class-exam types error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve class-exam type associations'
    });
  }
});

// Create or update class-exam type association
router.post('/class-exam-types', requireAcademicContext(), async (req, res) => {
  try {
    const { class_id, exam_type_id, academic_year_id, term_id, weight_percentage } = req.body;

    // Validate required fields
    if (!class_id || !exam_type_id || !academic_year_id || !term_id) {
      return res.status(400).json({
        success: false,
        message: 'Class ID, exam type ID, academic year ID, and term ID are required'
      });
    }

    // Validate weight_percentage if provided (must be integer 1-100)
    // Note: This represents the exam's share within the Summative Assessment portion (80%)
    if (weight_percentage !== undefined && (isNaN(weight_percentage) || !Number.isInteger(Number(weight_percentage)) || weight_percentage < 1 || weight_percentage > 100)) {
      return res.status(400).json({
        success: false,
        message: 'Weight percentage must be an integer between 1 and 100 (represents share within Summative Assessment portion)'
      });
    }

    // Use INSERT ... ON DUPLICATE KEY UPDATE for upsert functionality
    const upsertQuery = `
      INSERT INTO class_exam_types (class_id, exam_type_id, academic_year_id, term_id, weight_percentage)
      VALUES (?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        weight_percentage = VALUES(weight_percentage),
        updated_at = NOW()
    `;

    const result = await executeQuery(upsertQuery, [
      class_id, exam_type_id, academic_year_id, term_id, weight_percentage || 0
    ]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Validate that total weights for this class equal 100% (for summative assessment portion)
    const weightValidationQuery = `
      SELECT SUM(weight_percentage) as total_weight
      FROM class_exam_types
      WHERE class_id = ? AND academic_year_id = ? AND term_id = ?
    `;

    const weightValidationResult = await executeQuery(weightValidationQuery, [class_id, academic_year_id, term_id]);

    if (weightValidationResult.success && weightValidationResult.data.length > 0) {
      const totalWeight = weightValidationResult.data[0].total_weight || 0;

      // Log warning if weights don't total 100% (but don't block the operation)
      if (totalWeight !== 100) {
        console.warn(`Warning: Class ${class_id} exam weights total ${totalWeight}% instead of 100% for summative assessment portion`);
      }
    }

    res.json({
      success: true,
      message: 'Class-exam type association saved successfully'
    });

  } catch (error) {
    console.error('Save class-exam type association error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to save class-exam type association'
    });
  }
});

// Delete individual class-exam type association by ID
router.delete('/class-exam-types/:id', requireAcademicContext(), async (req, res) => {
  try {
    const { id } = req.params;

    // Validate required parameter
    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'Association ID is required'
      });
    }

    // Delete the association
    const deleteQuery = 'DELETE FROM class_exam_types WHERE id = ?';
    const result = await executeQuery(deleteQuery, [id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'Class-exam type association not found'
      });
    }

    res.json({
      success: true,
      message: 'Class-exam type association deleted successfully'
    });

  } catch (error) {
    console.error('Delete class-exam type association error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete class-exam type association'
    });
  }
});

// Validate class exam weights for two-tier assessment system
router.get('/class-exam-weights/validate/:classId', requireAcademicContext(), async (req, res) => {
  try {
    const { classId } = req.params;
    const { academic_year_id, term_id } = req.query;

    if (!academic_year_id || !term_id) {
      return res.status(400).json({
        success: false,
        message: 'Academic year ID and term ID are required'
      });
    }

    // Get all exam types for this class
    const query = `
      SELECT
        cet.exam_type_id,
        et.name as exam_type_name,
        et.short_name,
        cet.weight_percentage,
        SUM(cet.weight_percentage) OVER() as total_weight
      FROM class_exam_types cet
      JOIN exam_types et ON cet.exam_type_id = et.id
      WHERE cet.class_id = ?
        AND cet.academic_year_id = ?
        AND cet.term_id = ?
      ORDER BY et.sort_order
    `;

    const result = await executeQuery(query, [classId, academic_year_id, term_id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    const examTypes = result.data;
    const totalWeight = examTypes.length > 0 ? examTypes[0].total_weight : 0;
    const isValid = totalWeight === 100;

    res.json({
      success: true,
      data: {
        exam_types: examTypes,
        total_weight: totalWeight,
        is_valid: isValid,
        message: isValid
          ? 'Exam weights are properly configured for two-tier assessment system'
          : `Exam weights total ${totalWeight}% but should total 100% for summative assessment portion`
      }
    });

  } catch (error) {
    console.error('Validate class exam weights error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to validate class exam weights'
    });
  }
});

// =============================================
// TERM EXAMINATIONS ROUTES
// =============================================

// Get term examinations
router.get('/term-examinations', async (req, res) => {
  try {
    const { class_id, subject_id, academic_year_id, term_id, exam_type_id } = req.query;

    // Query both O-Level and A-Level examinations using UNION
    let query = `
      SELECT
        te.*,
        'o_level' as subject_level,
        c.name as class_name,
        et.name as exam_type_name,
        ay.name as academic_year_name,
        t.name as term_name,
        NULL as paper_number,
        NULL as paper_name
      FROM o_level_term_examinations te
      JOIN classes c ON te.class_id = c.id
      JOIN exam_types et ON te.exam_type_id = et.id
      JOIN academic_years ay ON te.academic_year_id = ay.id
      JOIN terms t ON te.term_id = t.id
      WHERE 1=1
    `;

    const params = [];

    // Add O-Level filters
    if (class_id) {
      query += ' AND te.class_id = ?';
      params.push(class_id);
    }
    if (subject_id) {
      query += ' AND te.subject_id = ?';
      params.push(subject_id);
    }
    if (academic_year_id) {
      query += ' AND te.academic_year_id = ?';
      params.push(academic_year_id);
    }
    if (term_id) {
      query += ' AND te.term_id = ?';
      params.push(term_id);
    }
    if (exam_type_id) {
      query += ' AND te.exam_type_id = ?';
      params.push(exam_type_id);
    }

    // Add A-Level examinations
    query += `
      UNION ALL
      SELECT
        te.*,
        'a_level' as subject_level,
        c.name as class_name,
        et.name as exam_type_name,
        ay.name as academic_year_name,
        t.name as term_name,
        sp.paper_number,
        sp.paper_name
      FROM a_level_paper_examinations te
      JOIN classes c ON te.class_id = c.id
      JOIN exam_types et ON te.exam_type_id = et.id
      JOIN academic_years ay ON te.academic_year_id = ay.id
      JOIN terms t ON te.term_id = t.id
      JOIN a_level_subject_papers sp ON te.subject_paper_id = sp.id
      WHERE 1=1
    `;

    // Add A-Level filters (duplicate parameters)
    if (class_id) {
      query += ' AND te.class_id = ?';
      params.push(class_id);
    }
    if (subject_id) {
      query += ' AND sp.subject_id = ?';
      params.push(subject_id);
    }
    if (academic_year_id) {
      query += ' AND te.academic_year_id = ?';
      params.push(academic_year_id);
    }
    if (term_id) {
      query += ' AND te.term_id = ?';
      params.push(term_id);
    }
    if (exam_type_id) {
      query += ' AND te.exam_type_id = ?';
      params.push(exam_type_id);
    }

    query += ' ORDER BY te.created_at DESC, c.name';

    query += ' ORDER BY te.created_at DESC';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get term examinations error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve term examinations'
    });
  }
});

// Create term examination
router.post('/term-examinations', async (req, res) => {
  try {
    const { class_id, subject_id, subject_paper_id, academic_year_id, term_id, exam_type_id, subject_level } = req.body;

    // Validate required fields
    if (!class_id || !academic_year_id || !term_id || !exam_type_id || !subject_level) {
      return res.status(400).json({
        success: false,
        message: 'Class ID, academic year ID, term ID, exam type ID, and subject level are required'
      });
    }

    // For O-Level, subject_id is required; for A-Level, subject_paper_id is required
    if (subject_level === 'o_level' && !subject_id) {
      return res.status(400).json({
        success: false,
        message: 'Subject ID is required for O-Level examinations'
      });
    }

    if (subject_level === 'a_level' && !subject_paper_id) {
      return res.status(400).json({
        success: false,
        message: 'Subject paper ID is required for A-Level examinations'
      });
    }

    let checkQuery, insertQuery, checkParams, insertParams;

    if (subject_level === 'o_level') {
      // O-Level examination
      checkQuery = `
        SELECT id FROM o_level_term_examinations
        WHERE class_id = ? AND subject_id = ? AND academic_year_id = ? AND term_id = ? AND exam_type_id = ?
      `;
      checkParams = [class_id, subject_id, academic_year_id, term_id, exam_type_id];

      insertQuery = `
        INSERT INTO o_level_term_examinations (
          class_id, subject_id, academic_year_id, term_id, exam_type_id
        ) VALUES (?, ?, ?, ?, ?)
      `;
      insertParams = [class_id, subject_id, academic_year_id, term_id, exam_type_id];
    } else {
      // A-Level examination
      checkQuery = `
        SELECT id FROM a_level_paper_examinations
        WHERE class_id = ? AND subject_paper_id = ? AND academic_year_id = ? AND term_id = ? AND exam_type_id = ?
      `;
      checkParams = [class_id, subject_paper_id, academic_year_id, term_id, exam_type_id];

      insertQuery = `
        INSERT INTO a_level_paper_examinations (
          class_id, subject_paper_id, academic_year_id, term_id, exam_type_id
        ) VALUES (?, ?, ?, ?, ?)
      `;
      insertParams = [class_id, subject_paper_id, academic_year_id, term_id, exam_type_id];
    }

    // Check if examination already exists
    const checkResult = await executeQuery(checkQuery, checkParams);

    if (!checkResult.success) {
      throw new Error(checkResult.error);
    }

    if (checkResult.data.length > 0) {
      // Examination already exists, return existing ID
      return res.json({
        success: true,
        data: { id: checkResult.data[0].id },
        message: 'Examination already exists'
      });
    }

    // Create new examination
    const result = await executeQuery(insertQuery, insertParams);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.status(201).json({
      success: true,
      data: { id: result.data.insertId },
      message: 'Term examination created successfully'
    });

  } catch (error) {
    console.error('Create term examination error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create term examination'
    });
  }
});

// =============================================
// A-LEVEL PAPER GRADES ENDPOINTS
// =============================================

// Get A-Level paper grade boundaries
router.get('/grade-boundaries/a-level-paper', async (req, res) => {
  try {
    const query = `
      SELECT
        id, grade_code, min_percentage, max_percentage,
        grade_descriptor, created_at, updated_at
      FROM a_level_paper_grade_boundaries
      ORDER BY min_percentage DESC
    `;

    const result = await executeQuery(query);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data,
      message: 'A-Level paper grade boundaries retrieved successfully'
    });

  } catch (error) {
    console.error('Get A-Level paper grade boundaries error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve A-Level paper grade boundaries',
      error: error.message
    });
  }
});

// Get student paper grades for a specific subject and term
router.get('/students/:studentId/paper-grades', async (req, res) => {
  try {
    const { studentId } = req.params;
    const { subjectId, termId, academicYearId } = req.query;

    // Dynamic calculation of paper grades from CA weights and exam marks
    let query = `
      SELECT DISTINCT
        caw.student_id, sp.subject_id, caw.academic_year_id, caw.term_id,
        sp.paper_number, caw.ca_average, caw.ca_weight_points,
        s.name as subject_name, s.short_name as subject_short_name, s.exam_papers,
        ay.name as academic_year, term.name as term_name,
        caw.calculated_at as created_at, caw.updated_at
      FROM a_level_paper_ca_weights caw
      INNER JOIN a_level_subject_papers sp ON caw.subject_paper_id = sp.id
      INNER JOIN a_level_subjects s ON sp.subject_id = s.id
      INNER JOIN academic_years ay ON caw.academic_year_id = ay.id
      INNER JOIN terms term ON caw.term_id = term.id
      WHERE caw.student_id = ?
    `;

    const params = [studentId];

    if (subjectId) {
      query += ' AND pcg.subject_id = ?';
      params.push(subjectId);
    }

    if (termId) {
      query += ' AND pcg.term_id = ?';
      params.push(termId);
    }

    if (academicYearId) {
      query += ' AND pcg.academic_year_id = ?';
      params.push(academicYearId);
    }

    query += ' ORDER BY pcg.subject_id, pcg.paper_number';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data,
      message: 'Student paper grades retrieved successfully'
    });

  } catch (error) {
    console.error('Get student paper grades error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve student paper grades',
      error: error.message
    });
  }
});

// Create or update paper CA
router.post('/students/:studentId/paper-ca', async (req, res) => {
  try {
    const { studentId } = req.params;
    const {
      subjectId, academicYearId, termId, paperNumber, caNumber, competencyScore
    } = req.body;

    // Validate required fields
    if (!subjectId || !academicYearId || !termId || !paperNumber || !caNumber || competencyScore === undefined) {
      return res.status(400).json({
        success: false,
        message: 'All fields are required: subjectId, academicYearId, termId, paperNumber, caNumber, competencyScore'
      });
    }

    // First get the subject_paper_id
    const paperQuery = `
      SELECT id FROM a_level_subject_papers
      WHERE subject_id = ? AND paper_number = ?
    `;

    const paperResult = await executeQuery(paperQuery, [subjectId, paperNumber]);

    if (!paperResult.success || paperResult.data.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid subject paper combination'
      });
    }

    const subjectPaperId = paperResult.data[0].id;

    // Check if CA already exists
    const checkQuery = `
      SELECT id FROM a_level_paper_continuous_assessments_scores
      WHERE student_id = ? AND subject_paper_id = ? AND term_id = ? AND ca_number = ?
    `;

    const checkResult = await executeQuery(checkQuery, [studentId, subjectPaperId, termId, caNumber]);

    if (!checkResult.success) {
      throw new Error(checkResult.error);
    }

    let query, params;

    if (checkResult.data.length > 0) {
      // Update existing record
      query = `
        UPDATE a_level_paper_continuous_assessments_scores SET
          competency_score = ?, updated_at = NOW()
        WHERE student_id = ? AND subject_paper_id = ? AND term_id = ? AND ca_number = ?
      `;
      params = [competencyScore, studentId, subjectPaperId, termId, caNumber];
    } else {
      // Insert new record
      query = `
        INSERT INTO a_level_paper_continuous_assessments_scores
        (student_id, subject_paper_id, academic_year_id, term_id, ca_number, competency_score)
        VALUES (?, ?, ?, ?, ?, ?)
      `;
      params = [studentId, subjectPaperId, academicYearId, termId, caNumber, competencyScore];
    }

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: { id: checkResult.data.length > 0 ? checkResult.data[0].id : result.data.insertId },
      message: checkResult.data.length > 0 ? 'Paper CA updated successfully' : 'Paper CA created successfully'
    });

  } catch (error) {
    console.error('Create/update paper CA error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to save paper CA',
      error: error.message
    });
  }
});

// Calculate paper grade from CAs and exams
router.post('/students/:studentId/calculate-paper-grade', async (req, res) => {
  try {
    const { studentId } = req.params;
    const { subjectId, termId, academicYearId, paperNumber } = req.body;

    // Validate required fields
    if (!subjectId || !termId || !academicYearId || !paperNumber) {
      return res.status(400).json({
        success: false,
        message: 'Subject ID, term ID, academic year ID, and paper number are required'
      });
    }

    // First get the subject_paper_id
    const paperQuery = `
      SELECT id FROM a_level_subject_papers
      WHERE subject_id = ? AND paper_number = ?
    `;

    const paperResult = await executeQuery(paperQuery, [subjectId, paperNumber]);

    if (!paperResult.success || paperResult.data.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid subject paper combination'
      });
    }

    const subjectPaperId = paperResult.data[0].id;

    // Get all CAs for this paper
    const caQuery = `
      SELECT competency_score
      FROM a_level_paper_continuous_assessments_scores
      WHERE student_id = ? AND subject_paper_id = ? AND term_id = ?
      ORDER BY ca_number
    `;

    const caResult = await executeQuery(caQuery, [studentId, subjectPaperId, termId]);

    if (!caResult.success) {
      throw new Error(caResult.error);
    }

    const caScores = caResult.data.map(ca => ca.competency_score);

    if (caScores.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No CA scores found for this paper'
      });
    }

    // Get pre-calculated weights from database (calculated by triggers)
    const weightsQuery = `
      SELECT
        caw.ca_weight_points,
        ew.exam_weight_points
      FROM a_level_paper_ca_weights caw
      LEFT JOIN a_level_paper_exam_weights ew ON caw.subject_paper_id = ew.subject_paper_id
        AND caw.student_id = ew.student_id AND caw.term_id = ew.term_id AND caw.academic_year_id = ew.academic_year_id
      WHERE caw.subject_paper_id = ? AND caw.student_id = ? AND caw.term_id = ? AND caw.academic_year_id = ?
    `;

    const weightsResult = await executeQuery(weightsQuery, [subjectPaperId, studentId, termId, academicYearId]);

    let caWeight = 0;
    let examWeight = 0;

    if (weightsResult.success && weightsResult.data.length > 0) {
      caWeight = weightsResult.data[0].ca_weight_points || 0;
      examWeight = weightsResult.data[0].exam_weight_points || 0;
    }

    // Calculate final score from pre-calculated weights
    const finalScore = caWeight + examWeight;

    // Determine paper grade based on UACE boundaries from database
    const paperGrade = await getPaperGradeFromPercentage(finalScore);
    const gradeDescriptor = paperGrade ? await getPaperGradeDescriptor(paperGrade) : null;

    if (!paperGrade) {
      return res.status(400).json({
        success: false,
        message: `No grade boundary found for score ${finalScore}%. Please check grade boundaries configuration.`
      });
    }

    // Note: All weights are automatically calculated by database triggers
    // No manual calculations needed - just retrieve pre-calculated values

    res.json({
      success: true,
      data: {
        caScores,
        caWeight: caWeight,
        examWeight: examWeight,
        finalScore,
        paperGrade,
        gradeDescriptor
      },
      message: 'Paper grade calculated successfully'
    });

  } catch (error) {
    console.error('Calculate paper grade error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to calculate paper grade',
      error: error.message
    });
  }
});

// Calculate final subject grade from paper grades using UACE rules
router.post('/students/:studentId/calculate-subject-grade', async (req, res) => {
  try {
    const { studentId } = req.params;
    const { subjectId, termId, academicYearId } = req.body;

    // Validate required fields
    if (!subjectId || !termId || !academicYearId) {
      return res.status(400).json({
        success: false,
        message: 'Subject ID, term ID, and academic year ID are required'
      });
    }

    // Get CA weights and exam marks to calculate paper grades dynamically
    const paperGradesQuery = `
      SELECT
        sp.paper_number,
        caw.ca_weight_points,
        em.marks_obtained as exam_percentage,
        (COALESCE(caw.ca_weight_points, 0) + COALESCE(ew.exam_weight_points, 0)) as final_score
      FROM a_level_subject_papers sp
      LEFT JOIN a_level_paper_ca_weights caw ON sp.id = caw.subject_paper_id
        AND caw.student_id = ? AND caw.term_id = ? AND caw.academic_year_id = ?
      LEFT JOIN a_level_paper_exam_weights ew ON sp.id = ew.subject_paper_id
        AND ew.student_id = ? AND ew.term_id = ? AND ew.academic_year_id = ?
      WHERE sp.subject_id = ?
      ORDER BY sp.paper_number
    `;

    const paperGradesResult = await executeQuery(paperGradesQuery, [
      studentId, termId, academicYearId, studentId, termId, academicYearId, subjectId
    ]);

    if (!paperGradesResult.success) {
      throw new Error(paperGradesResult.error);
    }

    const paperGrades = paperGradesResult.data;

    if (paperGrades.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No paper data found for this student and subject'
      });
    }

    // Calculate paper grades from final scores using grade boundaries
    for (let paper of paperGrades) {
      if (paper.final_score !== null) {
        const gradeQuery = `
          SELECT grade_code, grade_descriptor
          FROM a_level_paper_grade_boundaries
          WHERE ? >= min_percentage AND ? <= max_percentage
          LIMIT 1
        `;

        const gradeResult = await executeQuery(gradeQuery, [paper.final_score, paper.final_score]);

        if (gradeResult.success && gradeResult.data.length > 0) {
          paper.paper_grade = gradeResult.data[0].grade_code;
          paper.grade_descriptor = gradeResult.data[0].grade_descriptor;
        } else {
          paper.paper_grade = 'F9';
          paper.grade_descriptor = 'Fail 9';
        }
      } else {
        paper.paper_grade = null;
        paper.grade_descriptor = null;
      }
    }

    // Get subject information to determine number of papers
    const subjectQuery = `
      SELECT exam_papers, subject_type FROM a_level_subjects WHERE id = ?
    `;

    const subjectResult = await executeQuery(subjectQuery, [subjectId]);

    if (!subjectResult.success || subjectResult.data.length === 0) {
      throw new Error('Subject not found');
    }

    const subject = subjectResult.data[0];
    const numPapers = subject.exam_papers;
    const subjectType = subject.subject_type;

    // Calculate final grade using UACE rules
    let finalGrade;
    let gradePoints;

    if (subjectType === 'Principal') {
      // For Principal subjects, use UACE combination rules to get A, B, C, D, E, O, F
      finalGrade = calculateUACEGrade(paperGrades.map(p => p.paper_grade), numPapers);
      gradePoints = calculateGradePoints(finalGrade, subjectType);
    } else {
      // For Subsidiary subjects, calculate average percentage from all papers, then get UACE grade
      const validPapers = paperGrades.filter(p => p.final_score !== null && p.final_score !== undefined);
      if (validPapers.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'No valid paper scores found for subsidiary subject'
        });
      }

      const averagePercentage = validPapers.reduce((sum, p) => sum + p.final_score, 0) / validPapers.length;
      finalGrade = await getPaperGradeFromPercentage(averagePercentage);

      if (!finalGrade) {
        return res.status(400).json({
          success: false,
          message: `No grade boundary found for average score ${averagePercentage}%. Please check grade boundaries configuration.`
        });
      }

      gradePoints = calculateGradePoints(finalGrade, subjectType);
    }

    // Final grades are calculated dynamically - no need to store them
    const calculationMethod = numPapers === 2 ? 'TWO_PAPER' :
                             numPapers === 3 ? 'THREE_PAPER' : 'FOUR_PAPER';

    res.json({
      success: true,
      data: {
        paperGrades: paperGrades.map(p => p.paper_grade),
        finalGrade,
        gradePoints,
        calculationMethod,
        numPapers
      },
      message: 'Subject grade calculated successfully'
    });

  } catch (error) {
    console.error('Calculate subject grade error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to calculate subject grade',
      error: error.message
    });
  }
});

// Helper function to calculate UACE grade from paper grades using official UACE rules
function calculateUACEGrade(paperGrades, numPapers) {
  // Convert grade codes to numeric values for easier comparison (D1=1, D2=2, C3=3, etc.)
  const gradeValues = {
    'D1': 1, 'D2': 2, 'C3': 3, 'C4': 4, 'C5': 5, 'C6': 6, 'P7': 7, 'P8': 8, 'F9': 9
  };

  const grades = paperGrades.map(g => gradeValues[g] || 9).sort((a, b) => a - b);

  if (numPapers === 2) {
    // Two-Paper Subjects - UACE Official Rules
    const [best, worst] = grades;

    // Grade A: Distinctions in both papers (1,1; 1,2; 2,2)
    if (best <= 2 && worst <= 2) return 'A';

    // Grade B: At worst, a C3 in one paper and better in the second (1,3; 2,3; 3,3)
    if (worst <= 3) return 'B';

    // Grade C: At worst, a C4 in one paper and better in the second (1,4; 2,4; 3,4)
    if (worst <= 4) return 'C';

    // Grade D: At worst, a C5 in one paper and better in the second (1,5; 2,5; 3,5)
    if (worst <= 5) return 'D';

    // Grade E: At worst, a C6 in one paper and better in the second (1,6; 2,6; 3,6)
    if (worst <= 6) return 'E';

    // Grade O: At worst, a P7 in one paper and better in the second (1,7; 2,7; 3,7)
    if (worst <= 7) return 'O';

    // Grade F: At worst, an F9 in one paper and better in the second (1,9; 2,9; 3,9)
    return 'F';

  } else if (numPapers === 3) {
    // Three-Paper Subjects - UACE Official Rules
    const [best, middle, worst] = grades;

    // Grade A: At worst, a C3 in one paper and distinctions in the other two (1,1,3; 1,2,3; 2,2,3)
    if (worst <= 3 && middle <= 2 && best <= 2) return 'A';

    // Grade B: At worst, a C4 in one paper and better in the other two (1,1,4; 2,2,4; 3,3,4)
    if (worst <= 4 && middle <= 3 && best <= 3) return 'B';

    // Grade C: At worst, a C5 in one paper and better in the other two (1,1,5; 2,2,5; 3,3,5)
    if (worst <= 5 && middle <= 3 && best <= 3) return 'C';

    // Grade D: At worst, a C6 in one paper and better in the other two (1,1,6; 2,2,6; 3,3,6)
    if (worst <= 6 && middle <= 3 && best <= 3) return 'D';

    // Grade E: At worst, a P7 in one paper and better in the other two (1,1,7; 2,2,7; 3,3,7)
    if (worst <= 7 && middle <= 3 && best <= 3) return 'E';

    // Grade O: At worst, a P8 in one paper and better in the other two (1,1,8; 2,2,8; 3,3,8)
    if (worst <= 8 && middle <= 3 && best <= 3) return 'O';

    // Grade F: At worst, an F9 in one paper and better in the other two (1,1,9; 2,2,9; 3,3,9)
    return 'F';

  } else if (numPapers === 4) {
    // Four-Paper Subjects - UACE Official Rules
    const [best, second, third, worst] = grades;

    // Grade A: At worst, a C3 in one paper and distinctions in the other three (1,1,1,3; 2,2,2,3)
    if (worst <= 3 && third <= 2 && second <= 2 && best <= 2) return 'A';

    // Grade B: At worst, a C4 in one paper and better in the other three (1,1,1,4; 2,2,2,4)
    if (worst <= 4 && third <= 3 && second <= 3 && best <= 3) return 'B';

    // Grade C: At worst, a C5 in one paper and better in the other three (1,1,1,5; 2,2,2,5)
    if (worst <= 5 && third <= 3 && second <= 3 && best <= 3) return 'C';

    // Grade D: At worst, a C6 in one paper and better in the other three (1,1,1,6; 2,2,2,6)
    if (worst <= 6 && third <= 3 && second <= 3 && best <= 3) return 'D';

    // Grade E: At worst, a P7 in one paper and better in the other three (1,1,1,7; 2,2,2,7)
    if (worst <= 7 && third <= 3 && second <= 3 && best <= 3) return 'E';

    // Grade O: At worst, a P8 in one paper and better in the other three (1,1,1,8; 2,2,2,8)
    if (worst <= 8 && third <= 3 && second <= 3 && best <= 3) return 'O';

    // Grade F: At worst, an F9 in one paper and better in the other three (1,1,1,9; 2,2,2,9)
    return 'F';
  }

  return 'F'; // Default to F if unable to calculate
}

// Helper function to calculate UACE grade points using official UACE system
function calculateGradePoints(finalGrade, subjectType) {
  if (subjectType === 'Principal') {
    // Principal subjects UACE points: A=6, B=5, C=4, D=3, E=2, O=1, F=0
    const principalPoints = { 'A': 6, 'B': 5, 'C': 4, 'D': 3, 'E': 2, 'O': 1, 'F': 0 };
    return principalPoints[finalGrade] || 0;
  } else {
    // Subsidiary subjects (GP, ICT, Sub-Math) use UACE paper grades directly:
    // Distinction (D1–D2) = 1 point, Credit (C3–C6) = 1 point, Pass (P7–P8) = 0 points, Fail (F9) = 0 points
    if (finalGrade === 'D1' || finalGrade === 'D2') {
      return 1; // Distinction = 1 point
    } else if (finalGrade === 'C3' || finalGrade === 'C4' || finalGrade === 'C5' || finalGrade === 'C6') {
      return 1; // Credit = 1 point
    } else if (finalGrade === 'P7' || finalGrade === 'P8') {
      return 0; // Pass = 0 points
    } else if (finalGrade === 'F9') {
      return 0; // Fail = 0 points
    }
    return 0; // Default to 0 points
  }
}

// Cache for paper grade boundaries to avoid repeated database queries
let paperGradeBoundariesCache = null;

// Helper function to load and cache paper grade boundaries
async function loadPaperGradeBoundaries() {
  try {
    const query = `
      SELECT grade_code, min_percentage, max_percentage
      FROM a_level_paper_grade_boundaries
      ORDER BY min_percentage DESC
    `;

    const result = await executeQuery(query);

    if (result.success) {
      paperGradeBoundariesCache = result.data;
      console.log('📊 Paper grade boundaries loaded:', paperGradeBoundariesCache.length, 'boundaries');
    } else {
      console.error('Failed to load paper grade boundaries:', result.error);
    }
  } catch (error) {
    console.error('Error loading paper grade boundaries:', error);
  }
}

// Helper function to get paper grade from percentage using cached boundaries
async function getPaperGradeFromPercentage(percentage) {
  // Load boundaries if not cached
  if (!paperGradeBoundariesCache) {
    await loadPaperGradeBoundaries();
  }

  // Find matching boundary
  if (paperGradeBoundariesCache) {
    for (const boundary of paperGradeBoundariesCache) {
      if (percentage >= boundary.min_percentage && percentage <= boundary.max_percentage) {
        return boundary.grade_code;
      }
    }
  }

  return null; // Return null if no boundary found
}

// Initialize boundaries cache when module loads
loadPaperGradeBoundaries();

// Helper function to get paper grade descriptor from database
async function getPaperGradeDescriptor(paperGrade) {
  try {
    const query = `
      SELECT grade_descriptor
      FROM a_level_paper_grade_boundaries
      WHERE grade_code = ?
      LIMIT 1
    `;

    const result = await executeQuery(query, [paperGrade]);

    if (result.success && result.data.length > 0) {
      return result.data[0].grade_descriptor;
    }

    return null; // Return null if not found

  } catch (error) {
    console.error('Error getting paper grade descriptor:', error);
    return null;
  }
}

// Create A-Level paper grade boundary
router.post('/grade-boundaries/a-level-paper', authenticateToken, async (req, res) => {
  try {
    const { grade_code, min_percentage, max_percentage, grade_descriptor } = req.body;

    // Validate required fields
    if (!grade_code || min_percentage === undefined || max_percentage === undefined || !grade_descriptor) {
      return res.status(400).json({
        success: false,
        message: 'All fields are required: grade_code, min_percentage, max_percentage, grade_descriptor'
      });
    }

    // Check if grade already exists
    const checkQuery = 'SELECT id FROM a_level_paper_grade_boundaries WHERE grade_code = ?';
    const checkResult = await executeQuery(checkQuery, [grade_code]);

    if (checkResult.success && checkResult.data.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Grade boundary already exists'
      });
    }

    const insertQuery = `
      INSERT INTO a_level_paper_grade_boundaries
      (grade_code, min_percentage, max_percentage, grade_descriptor)
      VALUES (?, ?, ?, ?)
    `;

    const result = await executeQuery(insertQuery, [
      grade_code, min_percentage, max_percentage, grade_descriptor
    ]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Clear cache to reload boundaries
    paperGradeBoundariesCache = null;

    res.status(201).json({
      success: true,
      message: 'A-Level paper grade boundary created successfully',
      data: { id: result.data.insertId }
    });

  } catch (error) {
    console.error('Create A-Level paper grade boundary error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create A-Level paper grade boundary',
      error: error.message
    });
  }
});

// Update A-Level paper grade boundaries (bulk update)
router.put('/grade-boundaries/a-level-paper', authenticateToken, async (req, res) => {
  try {
    const { boundaries } = req.body;

    if (!boundaries || !Array.isArray(boundaries)) {
      return res.status(400).json({
        success: false,
        message: 'Boundaries array is required'
      });
    }

    // Update each boundary
    for (const boundary of boundaries) {
      const { id, grade_code, min_percentage, max_percentage, grade_descriptor } = boundary;

      // Check if boundary exists
      const checkQuery = 'SELECT id FROM a_level_paper_grade_boundaries WHERE id = ?';
      const checkResult = await executeQuery(checkQuery, [id]);

      if (checkResult.success && checkResult.data.length > 0) {
        // Update existing boundary
        const updateQuery = `
          UPDATE a_level_paper_grade_boundaries
          SET min_percentage = ?, max_percentage = ?, grade_descriptor = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `;

        const result = await executeQuery(updateQuery, [
          min_percentage, max_percentage, grade_descriptor, id
        ]);

        if (!result.success) {
          throw new Error(`Failed to update grade ${grade_code}: ${result.error}`);
        }
      } else {
        // Create new boundary if it doesn't exist
        const insertQuery = `
          INSERT INTO a_level_paper_grade_boundaries
          (grade_code, min_percentage, max_percentage, grade_descriptor)
          VALUES (?, ?, ?, ?)
        `;

        const result = await executeQuery(insertQuery, [
          grade_code, min_percentage, max_percentage, grade_descriptor
        ]);

        if (!result.success) {
          throw new Error(`Failed to create grade ${grade_code}: ${result.error}`);
        }
      }
    }

    res.json({
      success: true,
      message: 'A-Level paper grade boundaries updated successfully'
    });

  } catch (error) {
    console.error('Update A-Level paper grade boundaries error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update A-Level paper grade boundaries',
      error: error.message
    });
  }
});

// Update A-Level paper grade boundary (single)
router.put('/grade-boundaries/a-level-paper/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { grade_code, min_percentage, max_percentage, grade_descriptor } = req.body;

    // Validate required fields
    if (!grade_code || min_percentage === undefined || max_percentage === undefined || !grade_descriptor) {
      return res.status(400).json({
        success: false,
        message: 'All fields are required'
      });
    }

    // Check if boundary exists
    const checkQuery = 'SELECT id FROM a_level_paper_grade_boundaries WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Grade boundary not found'
      });
    }

    const updateQuery = `
      UPDATE a_level_paper_grade_boundaries
      SET grade_code = ?, min_percentage = ?, max_percentage = ?,
          grade_descriptor = ?, updated_at = NOW()
      WHERE id = ?
    `;

    const result = await executeQuery(updateQuery, [
      grade_code, min_percentage, max_percentage, grade_descriptor, id
    ]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Clear cache to reload boundaries
    paperGradeBoundariesCache = null;

    res.json({
      success: true,
      message: 'A-Level paper grade boundary updated successfully'
    });

  } catch (error) {
    console.error('Update A-Level paper grade boundary error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update A-Level paper grade boundary',
      error: error.message
    });
  }
});

// Delete A-Level paper grade boundary
router.delete('/grade-boundaries/a-level-paper/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if boundary exists
    const checkQuery = 'SELECT id FROM a_level_paper_grade_boundaries WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Grade boundary not found'
      });
    }

    // Hard delete since is_active column no longer exists
    const deleteQuery = 'DELETE FROM a_level_paper_grade_boundaries WHERE id = ?';
    const result = await executeQuery(deleteQuery, [id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Clear cache to reload boundaries
    paperGradeBoundariesCache = null;

    res.json({
      success: true,
      message: 'A-Level paper grade boundary deleted successfully'
    });

  } catch (error) {
    console.error('Delete A-Level paper grade boundary error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete A-Level paper grade boundary',
      error: error.message
    });
  }
});

// =============================================
// DEMONSTRATION: GET FINAL GRADES FROM PRE-CALCULATED WEIGHTS
// =============================================

// Get final grades for a student using only pre-calculated weights (no manual calculations)
router.get('/students/:studentId/final-grades', async (req, res) => {
  try {
    const { studentId } = req.params;
    const { academic_year_id, term_id, level = 'o_level' } = req.query;

    if (!academic_year_id || !term_id) {
      return res.status(400).json({
        success: false,
        message: 'Academic year ID and term ID are required'
      });
    }

    let gradesQuery;
    let params = [studentId, academic_year_id, term_id];

    if (level === 'o_level') {
      // O-Level: Get pre-calculated weights and sum them for final grades
      gradesQuery = `
        SELECT
          s.name as subject_name,
          s.code as subject_code,
          caw.ca_weight_points,
          ew.exam_weight_points,
          (COALESCE(caw.ca_weight_points, 0) + COALESCE(ew.exam_weight_points, 0)) as final_grade,
          'o_level' as level
        FROM o_level_subjects s
        LEFT JOIN o_level_subject_ca_weights caw ON s.id = caw.subject_id
          AND caw.student_id = ? AND caw.academic_year_id = ? AND caw.term_id = ?
        LEFT JOIN o_level_subject_exam_weights ew ON s.id = ew.subject_id
          AND ew.student_id = ? AND ew.academic_year_id = ? AND ew.term_id = ?
        WHERE (caw.ca_weight_points IS NOT NULL OR ew.exam_weight_points IS NOT NULL)
        ORDER BY s.name
      `;
      params = [studentId, academic_year_id, term_id, studentId, academic_year_id, term_id];
    } else {
      // A-Level: Get pre-calculated weights per paper and sum them for final grades
      gradesQuery = `
        SELECT
          s.name as subject_name,
          s.code as subject_code,
          sp.paper_number,
          caw.ca_weight_points,
          ew.exam_weight_points,
          (COALESCE(caw.ca_weight_points, 0) + COALESCE(ew.exam_weight_points, 0)) as final_grade,
          'a_level' as level
        FROM a_level_subjects s
        JOIN a_level_subject_papers sp ON s.id = sp.subject_id
        LEFT JOIN a_level_paper_ca_weights caw ON sp.id = caw.subject_paper_id
          AND caw.student_id = ? AND caw.academic_year_id = ? AND caw.term_id = ?
        LEFT JOIN a_level_paper_exam_weights ew ON sp.id = ew.subject_paper_id
          AND ew.student_id = ? AND ew.academic_year_id = ? AND ew.term_id = ?
        WHERE (caw.ca_weight_points IS NOT NULL OR ew.exam_weight_points IS NOT NULL)
        ORDER BY s.name, sp.paper_number
      `;
      params = [studentId, academic_year_id, term_id, studentId, academic_year_id, term_id];
    }

    const result = await executeQuery(gradesQuery, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Format the response
    const grades = result.data.map(row => ({
      subject: row.subject_name,
      subject_code: row.subject_code,
      paper_number: row.paper_number || null,
      ca_weight: row.ca_weight_points || 0,
      exam_weight: row.exam_weight_points || 0,
      final_grade: row.final_grade || 0,
      level: row.level,
      breakdown: {
        formative: `${row.ca_weight_points || 0}/20 (20%)`,
        summative: `${row.exam_weight_points || 0}/80 (80%)`,
        total: `${row.final_grade || 0}/100`
      }
    }));

    res.json({
      success: true,
      data: {
        student_id: studentId,
        academic_year_id: academic_year_id,
        term_id: term_id,
        level: level,
        grades: grades,
        summary: {
          total_subjects: grades.length,
          average_grade: grades.length > 0 ? Math.round(grades.reduce((sum, g) => sum + g.final_grade, 0) / grades.length) : 0
        }
      },
      message: 'Final grades retrieved successfully using pre-calculated weights'
    });

  } catch (error) {
    console.error('Get final grades error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve final grades',
      error: error.message
    });
  }
});

module.exports = router;

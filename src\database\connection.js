const mysql = require('mysql2/promise');
require('dotenv').config();

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'smartreport_db',
  charset: 'utf8mb4',
  timezone: '+00:00',
  multipleStatements: true
};

// Connection pool configuration
const poolConfig = {
  ...dbConfig,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

// Create connection pool
const pool = mysql.createPool(poolConfig);

// Test database connection
async function testConnection() {
  try {
    const connection = await pool.getConnection();
    console.log('✅ Database connected successfully');
    connection.release();
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

// Execute query with error handling
async function executeQuery(query, params = []) {
  try {
    const [rows] = await pool.execute(query, params);
    return { success: true, data: rows };
  } catch (error) {
    console.error('Database query error:', error);
    return { success: false, error: error.message };
  }
}

// Execute transaction
async function executeTransaction(queries) {
  const connection = await pool.getConnection();
  try {
    await connection.beginTransaction();
    
    const results = [];
    for (const { query, params } of queries) {
      const [rows] = await connection.execute(query, params || []);
      results.push(rows);
    }
    
    await connection.commit();
    return { success: true, data: results };
  } catch (error) {
    await connection.rollback();
    console.error('Transaction error:', error);
    return { success: false, error: error.message };
  } finally {
    connection.release();
  }
}

// Get database statistics
async function getDatabaseStats() {
  try {
    const queries = [
      'SELECT (SELECT COUNT(*) FROM o_level_students WHERE status = "active") + (SELECT COUNT(*) FROM a_level_students WHERE status = "active") as total_students',
      'SELECT COUNT(*) as total_teachers FROM teachers WHERE employment_status = "active"',
      'SELECT COUNT(*) as total_classes FROM classes WHERE is_active = 1',
      'SELECT (SELECT COUNT(*) FROM o_level_subjects WHERE is_active = 1) + (SELECT COUNT(*) FROM a_level_subjects WHERE is_active = 1) as total_subjects',
      'SELECT name as current_academic_year FROM academic_years WHERE is_active = 1',
      'SELECT t.name as current_term FROM terms t JOIN academic_years ay ON t.academic_year_id = ay.id WHERE t.is_active = 1 AND ay.is_active = 1'
    ];

    const results = await Promise.all(
      queries.map(query => executeQuery(query))
    );

    const stats = {};
    if (results[0].success) stats.totalStudents = results[0].data[0].total_students;
    if (results[1].success) stats.totalTeachers = results[1].data[0].total_teachers;
    if (results[2].success) stats.totalClasses = results[2].data[0].total_classes;
    if (results[3].success) stats.totalSubjects = results[3].data[0].total_subjects;
    if (results[4].success && results[4].data.length > 0) {
      stats.currentAcademicYear = results[4].data[0].current_academic_year;
    }
    if (results[5].success && results[5].data.length > 0) {
      stats.currentTerm = results[5].data[0].current_term;
    }

    return { success: true, data: stats };
  } catch (error) {
    console.error('Error getting database stats:', error);
    return { success: false, error: error.message };
  }
}

// Close all connections
async function closeConnections() {
  try {
    await pool.end();
    console.log('Database connections closed');
  } catch (error) {
    console.error('Error closing database connections:', error);
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('Received SIGINT, closing database connections...');
  await closeConnections();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Received SIGTERM, closing database connections...');
  await closeConnections();
  process.exit(0);
});

module.exports = {
  pool,
  testConnection,
  executeQuery,
  executeTransaction,
  getDatabaseStats,
  closeConnections
};

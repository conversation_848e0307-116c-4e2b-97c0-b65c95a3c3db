// SmartReport - Image Upload Utility
// Generic image upload functionality for all components

const ImageUploadUtil = {
  // Upload types and their corresponding API endpoints (based on actual database schema)
  uploadTypes: {
    'school-logo': '/api/upload/school-logo',
    'teacher-photo': '/api/upload/teacher-photo',
    'o-level-student-photo': '/api/upload/o-level-student-photo',
    'a-level-student-photo': '/api/upload/a-level-student-photo',
    'system-user-photo': '/api/upload/system-user-photo'
  },

  // Default settings
  defaultSettings: {
    maxSize: 2 * 1024 * 1024, // 2MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png'],
    allowedExtensions: ['.jpg', '.jpeg', '.png']
  },

  /**
   * Validate image file
   * @param {File} file - The file to validate
   * @param {Object} options - Validation options
   * @returns {Object} - Validation result
   */
  validateFile(file, options = {}) {
    const settings = { ...this.defaultSettings, ...options };

    if (!file) {
      return { valid: false, error: 'No file selected' };
    }

    // Check file type
    if (!settings.allowedTypes.includes(file.type)) {
      return { 
        valid: false, 
        error: `Invalid file type. Allowed types: ${settings.allowedExtensions.join(', ')}` 
      };
    }

    // Check file size
    if (file.size > settings.maxSize) {
      const maxSizeMB = settings.maxSize / (1024 * 1024);
      return { 
        valid: false, 
        error: `File size too large. Maximum size: ${maxSizeMB}MB` 
      };
    }

    return { valid: true };
  },

  /**
   * Upload image file
   * @param {File} file - The file to upload
   * @param {string} uploadType - Type of upload (school-logo, teacher-photo, etc.)
   * @param {Object} options - Upload options
   * @returns {Promise} - Upload result
   */
  async uploadImage(file, uploadType, options = {}) {
    try {
      // Validate file
      const validation = this.validateFile(file, options);
      if (!validation.valid) {
        throw new Error(validation.error);
      }

      // Check if upload type is supported
      const endpoint = this.uploadTypes[uploadType];
      if (!endpoint) {
        throw new Error(`Unsupported upload type: ${uploadType}`);
      }

      // Create FormData
      const formData = new FormData();
      const fieldName = uploadType === 'school-logo' ? 'logo' : 'photo';
      formData.append(fieldName, file);

      // Upload file
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('smartreport_token')}`
        },
        body: formData
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Upload failed');
      }

      return result;

    } catch (error) {
      console.error('Image upload error:', error);
      throw error;
    }
  },

  /**
   * Handle image upload with preview update
   * @param {HTMLInputElement} input - File input element
   * @param {string} uploadType - Type of upload
   * @param {string} previewId - ID of preview image element
   * @param {string} pathFieldId - ID of hidden field to store file path
   * @param {Object} options - Upload options
   */
  async handleImageUpload(input, uploadType, previewId, pathFieldId, options = {}) {
    if (!input.files || !input.files[0]) return;

    const file = input.files[0];

    try {
      // Show loading state if callback provided
      if (options.onUploadStart) {
        options.onUploadStart();
      }

      // Upload the file
      const result = await this.uploadImage(file, uploadType, options);

      if (result.success) {
        // Update preview image
        const preview = document.getElementById(previewId);
        if (preview) {
          preview.src = result.filePath;
        }

        // Update hidden field with file path
        const pathField = document.getElementById(pathFieldId);
        if (pathField) {
          pathField.value = result.filePath;
        }

        // Show success notification
        this.showNotification('Image uploaded successfully!', 'success');

        // Call success callback if provided
        if (options.onUploadSuccess) {
          options.onUploadSuccess(result);
        }

        return result;
      } else {
        throw new Error(result.message || 'Upload failed');
      }

    } catch (error) {
      // Show error notification
      this.showNotification('Failed to upload image: ' + error.message, 'error');
      
      // Clear file input
      input.value = '';

      // Call error callback if provided
      if (options.onUploadError) {
        options.onUploadError(error);
      }

      throw error;
    }
  },

  /**
   * Show notification using SRDesignSystem or fallback to alert
   * @param {string} message - Notification message
   * @param {string} type - Notification type (success, error, warning, info)
   */
  showNotification(message, type = 'info') {
    if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
      window.SRDesignSystem.notifications.show(message, type);
    } else {
      alert(message);
    }
  },

  /**
   * Create image upload HTML with preview
   * @param {Object} config - Configuration object
   * @returns {string} - HTML string
   */
  createImageUploadHTML(config) {
    const {
      fieldId,
      fieldName,
      uploadType,
      previewId,
      pathFieldId,
      currentImagePath = '',
      label = 'Upload Image',
      placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iI0Y5RkFGQiIvPgo8cGF0aCBkPSJNMjAgMjBIMjhWMjhIMjBWMjBaIiBmaWxsPSIjRDFENU5CIi8+CjxwYXRoIGQ9Ik0zNiAyMEg0NFYyOEgzNlYyMFoiIGZpbGw9IiNEMUQ1REIiLz4KPHBhdGggZD0iTTIwIDM2SDI4VjQ0SDIwVjM2WiIgZmlsbD0iI0QxRDVEQiIvPgo8cGF0aCBkPSJNMzYgMzZINDRWNDRIMzZWMzZaIiBmaWxsPSIjRDFENURCIi8+Cjx0ZXh0IHg9IjMyIiB5PSIzNCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjgiIGZpbGw9IiM5Q0EzQUYiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkxvZ288L3RleHQ+Cjwvc3ZnPg==',
      accept = 'image/*',
      required = false,
      helpText = 'Upload PNG, JPG, or GIF. Max size: 2MB'
    } = config;

    return `
      <div>
        <label for="${fieldId}" class="block text-sm font-medium text-gray-700 mb-2">${label}${required ? ' *' : ''}</label>
        <div class="flex items-center space-x-4">
          <div class="flex-shrink-0">
            <img id="${previewId}" 
                 src="${currentImagePath || placeholder}" 
                 alt="${label}" 
                 class="w-16 h-16 object-cover rounded-lg border border-gray-300"
                 onerror="this.src='${placeholder}'">
          </div>
          <div class="flex-1">
            <input type="file"
                   id="${fieldId}"
                   name="${fieldName}"
                   accept="${accept}"
                   ${required ? 'required' : ''}
                   onchange="ImageUploadUtil.handleImageUpload(this, '${uploadType}', '${previewId}', '${pathFieldId}')"
                   class="w-full px-4 py-3 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
            <p class="text-sm text-gray-500 mt-1">${helpText}</p>
            <input type="hidden" id="${pathFieldId}" name="${pathFieldId}" value="${currentImagePath}">
          </div>
        </div>
      </div>
    `;
  }
};

// Export to global scope
window.ImageUploadUtil = ImageUploadUtil;

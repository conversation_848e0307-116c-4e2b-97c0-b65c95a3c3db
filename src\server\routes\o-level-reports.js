const express = require('express');
const { executeQuery } = require('../../database/connection');
const { authenticateToken } = require('./auth');
const PDFOLevelReportGenerator = require('../services/pdf-o-level-report-generator');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// =============================================
// SYSTEM REPORTS
// =============================================

// Get system statistics
router.get('/system/stats', async (req, res) => {
  try {
    const systemStatsQuery = `
      SELECT
        (SELECT COUNT(*) FROM o_level_students WHERE status = 'active') +
        (SELECT COUNT(*) FROM a_level_students WHERE status = 'active') as total_students,
        (SELECT COUNT(*) FROM o_level_students WHERE status = 'active') as total_o_level_students,
        (SELECT COUNT(*) FROM a_level_students WHERE status = 'active') as total_a_level_students,
        (SELECT COUNT(*) FROM teachers WHERE is_active = TRUE) as total_teachers,
        (SELECT COUNT(*) FROM classes WHERE is_active = TRUE) as total_classes,
        (SELECT COUNT(*) FROM o_level_subjects WHERE is_active = TRUE) +
        (SELECT COUNT(*) FROM a_level_subjects WHERE is_active = TRUE) as total_subjects,
        (SELECT COUNT(*) FROM o_level_subject_continuous_assessments_scores) +
        (SELECT COUNT(*) FROM a_level_paper_continuous_assessments_scores) as total_assessments,
        (SELECT COUNT(*) FROM system_users WHERE is_active = TRUE) as total_system_users
    `;

    const result = await executeQuery(systemStatsQuery);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data[0]
    });

  } catch (error) {
    console.error('Get system stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve system statistics'
    });
  }
});

// Get user activity report
router.get('/system/user-activity', async (req, res) => {
  try {
    const activityQuery = `
      SELECT 
        su.username,
        su.first_name,
        su.last_name,
        su.email,
        su.last_login,
        su.created_at,
        CASE 
          WHEN su.last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 'Active'
          WHEN su.last_login >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 'Recent'
          ELSE 'Inactive'
        END as activity_status
      FROM system_users su
      WHERE su.is_active = TRUE
      ORDER BY su.last_login DESC
    `;
    
    const result = await executeQuery(activityQuery);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get user activity error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve user activity report'
    });
  }
});

// =============================================
// ACADEMIC REPORTS
// =============================================

// Get class performance report
router.get('/academic/class/:classId/performance', async (req, res) => {
  try {
    const { classId } = req.params;
    const { term_id, academic_year_id } = req.query;

    let whereClause = 'WHERE s.current_class_id = ?';
    let params = [classId];

    if (term_id) {
      whereClause += ' AND ca.term_id = ?';
      params.push(term_id);
    }

    if (academic_year_id) {
      whereClause += ' AND ca.academic_year_id = ?';
      params.push(academic_year_id);
    }

    // O-Level performance query
    const oLevelPerformanceQuery = `
      SELECT
        'o_level' as student_type,
        s.admission_number,
        s.first_name,
        s.last_name,
        sub.name as subject_name,
        sub.short_name as subject_short_name,
        COUNT(ca.id) as total_assessments,
        AVG(ca.competency_score) as average_score,
        MAX(ca.competency_score) as highest_score,
        MIN(ca.competency_score) as lowest_score,
        COUNT(CASE WHEN ca.competency_score >= 2.5 THEN 1 END) as excellent_count,
        COUNT(CASE WHEN ca.competency_score >= 2.0 AND ca.competency_score < 2.5 THEN 1 END) as good_count,
        COUNT(CASE WHEN ca.competency_score >= 1.5 AND ca.competency_score < 2.0 THEN 1 END) as satisfactory_count,
        COUNT(CASE WHEN ca.competency_score < 1.5 THEN 1 END) as needs_improvement_count
      FROM o_level_students s
      LEFT JOIN o_level_subject_continuous_assessments_scores ca ON s.id = ca.student_id
      LEFT JOIN o_level_subjects sub ON ca.subject_id = sub.id
      ${whereClause}
      GROUP BY s.id, s.admission_number, s.first_name, s.last_name, sub.id, sub.name, sub.short_name
    `;

    // A-Level performance query (Principal subjects)
    const aLevelPrincipalQuery = `
      SELECT
        'a_level_principal' as student_type,
        s.admission_number,
        s.first_name,
        s.last_name,
        sub.name as subject_name,
        sub.short_name as subject_short_name,
        COUNT(ca.id) as total_assessments,
        AVG(ca.competency_score) as average_score,
        MAX(ca.competency_score) as highest_score,
        MIN(ca.competency_score) as lowest_score,
        COUNT(CASE WHEN ca.competency_score >= 2.5 THEN 1 END) as excellent_count,
        COUNT(CASE WHEN ca.competency_score >= 2.0 AND ca.competency_score < 2.5 THEN 1 END) as good_count,
        COUNT(CASE WHEN ca.competency_score >= 1.5 AND ca.competency_score < 2.0 THEN 1 END) as satisfactory_count,
        COUNT(CASE WHEN ca.competency_score < 1.5 THEN 1 END) as needs_improvement_count
      FROM a_level_students s
      LEFT JOIN a_level_paper_continuous_assessments_scores ca ON s.id = ca.student_id
      LEFT JOIN a_level_subject_papers sp ON ca.subject_paper_id = sp.id
      LEFT JOIN a_level_subjects sub ON sp.subject_id = sub.id
      ${whereClause.replace('s.current_class_id', 's.current_class_id')}
      GROUP BY s.id, s.admission_number, s.first_name, s.last_name, sub.id, sub.name, sub.short_name
    `;

    // Execute both queries and combine results
    const [oLevelResult, aLevelResult] = await Promise.all([
      executeQuery(oLevelPerformanceQuery, params),
      executeQuery(aLevelPrincipalQuery, params)
    ]);

    const combinedData = [
      ...(oLevelResult.success ? oLevelResult.data : []),
      ...(aLevelResult.success ? aLevelResult.data : [])
    ].sort((a, b) => a.admission_number.localeCompare(b.admission_number));

    res.json({
      success: true,
      data: combinedData
    });

  } catch (error) {
    console.error('Get class performance error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve class performance report'
    });
  }
});

// Get subject performance report
router.get('/academic/subject/:subjectId/performance', async (req, res) => {
  try {
    const { subjectId } = req.params;
    const { term } = req.query;

    let whereClause = 'WHERE ca.subject_id = ?';
    let params = [subjectId];
    
    if (term) {
      whereClause += ' AND ca.term_id = ?';
      params.push(term);
    }

    const performanceQuery = `
      SELECT
        c.name as class_name,
        el.name as level_name,
        COUNT(DISTINCT all_assessments.student_id) as students_assessed,
        COUNT(all_assessments.id) as total_assessments,
        AVG(all_assessments.competency_score) as average_score,
        COUNT(CASE WHEN all_assessments.competency_score >= 2.5 THEN 1 END) as excellent_count,
        COUNT(CASE WHEN all_assessments.competency_score >= 2.0 AND all_assessments.competency_score < 2.5 THEN 1 END) as good_count,
        COUNT(CASE WHEN all_assessments.competency_score >= 1.5 AND all_assessments.competency_score < 2.0 THEN 1 END) as satisfactory_count,
        COUNT(CASE WHEN all_assessments.competency_score < 1.5 THEN 1 END) as needs_improvement_count
      FROM (
        SELECT ca.id, ca.student_id, ca.competency_score, ca.academic_year_id, ca.term_id, s.current_class_id
        FROM o_level_subject_continuous_assessments_scores ca
        JOIN o_level_students s ON ca.student_id = s.id
        WHERE s.status = 'active'
        UNION ALL
        SELECT ca.id, ca.student_id, ca.competency_score, ca.academic_year_id, ca.term_id, s.current_class_id
        FROM a_level_paper_continuous_assessments_scores ca
        JOIN a_level_students s ON ca.student_id = s.id
        WHERE s.status = 'active'
      ) all_assessments
      JOIN classes c ON all_assessments.current_class_id = c.id
      LEFT JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN education_levels el ON cl.education_level_id = el.id
      ${whereClause.replace('ca.', 'all_assessments.')}
      GROUP BY c.id, c.name, cl.name, el.name
      ORDER BY cl.sort_order, c.name
    `;
    
    const result = await executeQuery(performanceQuery, params);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get subject performance error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve subject performance report'
    });
  }
});

// =============================================
// STUDENT REPORTS
// =============================================

// Get comprehensive student report data for O-Level report cards
router.post('/student/:studentId/comprehensive-report', async (req, res) => {
  try {
    const { studentId } = req.params;
    const { term_id, academic_year_id, class_id } = req.body;

    if (!term_id || !academic_year_id || !class_id) {
      return res.status(400).json({
        success: false,
        message: 'Term ID, Academic Year ID, and Class ID are required'
      });
    }

    // Get student information
    const studentQuery = `
      SELECT
        s.id, s.admission_number, s.first_name, s.last_name, s.date_of_birth,
        s.gender, s.current_class_id, s.passport_photo,
        'o_level' as student_type
      FROM o_level_students s
      WHERE s.id = ? AND s.status = 'active'
    `;

    const studentResult = await executeQuery(studentQuery, [studentId]);
    if (!studentResult.success || studentResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }

    // Get subjects that the student is registered to take with teacher initials
    const subjectsQuery = `
      SELECT DISTINCT
        sub.id, sub.name, sub.short_name, sub.uneb_code as code,
        ca_config.total_cas,
        t.initials as teacher_initials
      FROM o_level_subjects sub
      INNER JOIN o_level_student_subjects ss ON sub.id = ss.subject_id
      LEFT JOIN o_level_ca_configuration ca_config ON sub.id = ca_config.subject_id
        AND ca_config.class_id = ? AND ca_config.term_id = ? AND ca_config.academic_year_id = ?
      LEFT JOIN class_subject_assignments csa ON sub.id = csa.subject_id
        AND csa.class_id = ? AND csa.academic_year_id = ? AND csa.subject_level = 'o_level'
      LEFT JOIN teachers t ON csa.teacher_id = t.id AND t.employment_status = 'active'
      WHERE sub.is_active = TRUE
        AND ss.student_id = ?
      ORDER BY sub.name
    `;

    const subjectsResult = await executeQuery(subjectsQuery, [class_id, term_id, academic_year_id, class_id, academic_year_id, studentId]);

    // Get formative assessments (CA 1-6) with proper competency score handling
    const formativeQuery = `
      SELECT
        ca.subject_id,
        MAX(CASE WHEN ca.ca_number = 1 THEN ca.competency_score END) as ca1,
        MAX(CASE WHEN ca.ca_number = 2 THEN ca.competency_score END) as ca2,
        MAX(CASE WHEN ca.ca_number = 3 THEN ca.competency_score END) as ca3,
        MAX(CASE WHEN ca.ca_number = 4 THEN ca.competency_score END) as ca4,
        MAX(CASE WHEN ca.ca_number = 5 THEN ca.competency_score END) as ca5,
        MAX(CASE WHEN ca.ca_number = 6 THEN ca.competency_score END) as ca6,
        AVG(CASE WHEN ca.competency_score IS NOT NULL THEN ca.competency_score END) as average,
        (AVG(CASE WHEN ca.competency_score IS NOT NULL THEN ca.competency_score END) / 3.0) * 20 as totalPoints,
        COUNT(CASE WHEN ca.competency_score IS NOT NULL THEN 1 END) as assessments_done
      FROM o_level_subject_continuous_assessments_scores ca
      WHERE ca.student_id = ? AND ca.term_id = ? AND ca.academic_year_id = ?
      GROUP BY ca.subject_id
    `;

    const formativeResult = await executeQuery(formativeQuery, [studentId, term_id, academic_year_id]);

    // Get class exam types configuration
    const classExamTypesQuery = `
      SELECT
        et.id as exam_type_id,
        et.name as exam_type_name,
        et.short_name,
        et.sort_order,
        cet.weight_percentage,
        cet.weight_percentage as max_marks
      FROM exam_types et
      JOIN class_exam_types cet ON et.id = cet.exam_type_id
      WHERE cet.class_id = ? AND cet.term_id = ? AND cet.academic_year_id = ?
      ORDER BY et.sort_order
    `;

    const classExamTypesResult = await executeQuery(classExamTypesQuery, [class_id, term_id, academic_year_id]);

    // Get summative assessments (exam marks) with proper exam type ordering
    const summativeQuery = `
      SELECT
        em.student_id,
        te.subject_id,
        te.exam_type_id,
        et.short_name as exam_type_name,
        et.sort_order,
        em.marks_obtained,
        cet.weight_percentage
      FROM o_level_student_exam_marks em
      JOIN o_level_term_examinations te ON em.examination_id = te.id
      JOIN exam_types et ON te.exam_type_id = et.id
      JOIN class_exam_types cet ON et.id = cet.exam_type_id
        AND cet.class_id = ? AND cet.term_id = ? AND cet.academic_year_id = ?
      WHERE em.student_id = ? AND te.term_id = ? AND te.academic_year_id = ?
      ORDER BY te.subject_id, et.sort_order
    `;

    const summativeResult = await executeQuery(summativeQuery, [class_id, term_id, academic_year_id, studentId, term_id, academic_year_id]);

    // Get grade boundaries
    const gradeBoundariesQuery = `
      SELECT grade_letter, min_percentage, max_percentage, grade_descriptor
      FROM o_level_grade_boundaries
      ORDER BY min_percentage DESC
    `;

    const gradeBoundariesResult = await executeQuery(gradeBoundariesQuery, []);

    // Get next term information
    const nextTermQuery = `
      SELECT name, start_date
      FROM terms
      WHERE start_date > (
        SELECT end_date FROM terms WHERE id = ?
      )
      ORDER BY start_date ASC
      LIMIT 1
    `;

    const nextTermResult = await executeQuery(nextTermQuery, [term_id]);

    // Get school settings
    const schoolSettingsQuery = `
      SELECT setting_key, setting_value
      FROM school_settings
    `;

    const schoolSettingsResult = await executeQuery(schoolSettingsQuery, []);

    // Process school settings into object
    const schoolSettings = {};
    if (schoolSettingsResult.success) {
      schoolSettingsResult.data.forEach(setting => {
        schoolSettings[setting.setting_key] = setting.setting_value;
      });
    }

    // Process summative data by subject with proper exam type ordering
    const summativeBySubject = {};
    if (summativeResult.success && classExamTypesResult.success) {
      // Initialize structure for each subject with ordered exam types
      subjectsResult.data.forEach(subject => {
        summativeBySubject[subject.id] = {
          examScores: new Array(classExamTypesResult.data.length).fill(null),
          examTypeNames: classExamTypesResult.data.map(et => et.short_name),
          total: 0
        };
      });

      // Fill in actual exam scores in correct order
      summativeResult.data.forEach(row => {
        if (summativeBySubject[row.subject_id]) {
          // Find the index of this exam type in the ordered list
          const examTypeIndex = classExamTypesResult.data.findIndex(et => et.exam_type_id === row.exam_type_id);
          if (examTypeIndex !== -1) {
            summativeBySubject[row.subject_id].examScores[examTypeIndex] = row.marks_obtained || 0;

            // Calculate weighted total for summative assessment
            const weightedScore = (row.marks_obtained || 0) * (row.weight_percentage / 100);
            summativeBySubject[row.subject_id].total += weightedScore;
          }
        }
      });
    }

    // Get pre-calculated weights for all subjects (calculated by triggers)
    const weightsQuery = `
      SELECT
        caw.subject_id,
        caw.ca_weight_points,
        ew.exam_weight_points,
        (COALESCE(caw.ca_weight_points, 0) + COALESCE(ew.exam_weight_points, 0)) as total_mark
      FROM o_level_subject_ca_weights caw
      LEFT JOIN o_level_subject_exam_weights ew ON caw.subject_id = ew.subject_id
        AND caw.student_id = ew.student_id AND caw.term_id = ew.term_id AND caw.academic_year_id = ew.academic_year_id
      WHERE caw.student_id = ? AND caw.term_id = ? AND caw.academic_year_id = ?
    `;

    const weightsResult = await executeQuery(weightsQuery, [studentId, term_id, academic_year_id]);

    // Calculate term averages using pre-calculated weights
    const termAverages = [];
    if (weightsResult.success && subjectsResult.success) {
      subjectsResult.data.forEach(subject => {
        const weights = weightsResult.data.find(w => w.subject_id === subject.id);

        if (weights && weights.total_mark > 0) {
          const totalMark = weights.total_mark;

          // Get grade based on total mark
          let grade = 'F';
          if (gradeBoundariesResult.success) {
            for (const boundary of gradeBoundariesResult.data) {
              if (totalMark >= boundary.min_percentage) {
                grade = boundary.grade_letter;
                break;
              }
            }
          }

          termAverages.push({
            subject_id: subject.id,
            totalMark: totalMark,
            grade: grade
          });
        }
      });
    }

    // Calculate overall summary
    const overallSummary = {
      subjectsTaken: subjectsResult.success ? subjectsResult.data.length : 0,
      totalMarks: 0,
      maxMarks: 0,
      average: 0,
      overallGrade: '-'
    };

    if (termAverages.length > 0) {
      const totalMarks = termAverages.reduce((sum, avg) => sum + avg.totalMark, 0);
      const maxMarks = termAverages.length * 100;
      const average = totalMarks / termAverages.length;

      overallSummary.totalMarks = Math.round(totalMarks * 10) / 10;
      overallSummary.maxMarks = maxMarks;
      overallSummary.average = Math.round(average * 10) / 10;

      // Get overall grade
      if (gradeBoundariesResult.success) {
        for (const boundary of gradeBoundariesResult.data) {
          if (average >= boundary.min_percentage) {
            overallSummary.overallGrade = boundary.grade_letter;
            break;
          }
        }
      }
    } else {
      // No complete subject data available - show dash instead of F
      overallSummary.overallGrade = '-';
    }

    // Get academic context information
    const academicContextQuery = `
      SELECT
        ay.id as academic_year_id, ay.name as academic_year_name, ay.start_date as ay_start, ay.end_date as ay_end,
        t.id as term_id, t.name as term_name, t.start_date as term_start, t.end_date as term_end,
        c.id as class_id, c.name as class_name,
        cl.name as class_level_name,
        s.name as stream_name
      FROM academic_years ay, terms t, classes c
      LEFT JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN streams s ON s.id = (SELECT stream_id FROM o_level_students WHERE id = ?)
      WHERE ay.id = ? AND t.id = ? AND c.id = ?
    `;

    const academicContextResult = await executeQuery(academicContextQuery, [studentId, academic_year_id, term_id, class_id]);

    // Prepare response data
    const reportData = {
      student: studentResult.data[0],
      subjects: subjectsResult.success ? subjectsResult.data : [],
      formativeAssessments: formativeResult.success ? formativeResult.data : [],
      summativeAssessments: Object.keys(summativeBySubject).map(subjectId => ({
        subject_id: parseInt(subjectId),
        examScores: summativeBySubject[subjectId].examScores,
        total: Math.round(summativeBySubject[subjectId].total * 10) / 10
      })),
      termAverages: termAverages,
      classExamTypes: classExamTypesResult.success ? classExamTypesResult.data : [],
      gradingScale: gradeBoundariesResult.success ? gradeBoundariesResult.data : [],
      overallSummary: overallSummary,
      nextTerm: nextTermResult.success && nextTermResult.data.length > 0 ? nextTermResult.data[0] : null,
      schoolSettings: schoolSettings,
      academicContext: academicContextResult.success && academicContextResult.data.length > 0 ? academicContextResult.data[0] : null
    };

    res.json({
      success: true,
      data: reportData
    });

  } catch (error) {
    console.error('Comprehensive report error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate comprehensive report'
    });
  }
});

// Get student report card
router.get('/student/:studentId/report-card', async (req, res) => {
  try {
    const { studentId } = req.params;
    const { term } = req.query;

    if (!term) {
      return res.status(400).json({
        success: false,
        message: 'Term parameter is required'
      });
    }

    // Get student basic info - try both O-Level and A-Level tables
    const oLevelStudentQuery = `
      SELECT
        s.*,
        s.current_class_id as class_id,
        c.name as class_name,
        cl.name as class_level_name,
        el.name as education_level_name,
        st.name as stream_name,
        ay.name as academic_year_name,
        t.name as term_name,
        'o_level' as student_type
      FROM o_level_students s
      LEFT JOIN classes c ON s.current_class_id = c.id
      LEFT JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN streams st ON c.stream_id = st.id
      LEFT JOIN academic_years ay ON s.current_academic_year_id = ay.id
      LEFT JOIN terms t ON s.current_term_id = t.id
      WHERE s.id = ?
    `;

    const aLevelStudentQuery = `
      SELECT
        s.*,
        s.current_class_id as class_id,
        c.name as class_name,
        cl.name as class_level_name,
        el.name as education_level_name,
        st.name as stream_name,
        ay.name as academic_year_name,
        t.name as term_name,
        'a_level' as student_type
      FROM a_level_students s
      LEFT JOIN classes c ON s.current_class_id = c.id
      LEFT JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN streams st ON c.stream_id = st.id
      LEFT JOIN academic_years ay ON s.current_academic_year_id = ay.id
      LEFT JOIN terms t ON s.current_term_id = t.id
      WHERE s.id = ?
    `;

    // Try to find student in O-Level table first
    let studentResult = await executeQuery(oLevelStudentQuery, [studentId]);

    if (!studentResult.success) {
      throw new Error(studentResult.error);
    }

    // If not found in O-Level, try A-Level
    if (studentResult.data.length === 0) {
      studentResult = await executeQuery(aLevelStudentQuery, [studentId]);

      if (!studentResult.success) {
        throw new Error(studentResult.error);
      }
    }

    if (studentResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }

    // Get student O-Level assessments for the term
    const oLevelAssessmentsQuery = `
      SELECT
        sub.name as subject_name,
        sub.short_name as subject_short_name,
        COUNT(ca.id) as total_assessments,
        AVG(ca.competency_score) as average_score,
        MAX(ca.competency_score) as highest_score,
        MIN(ca.competency_score) as lowest_score,
        'O-Level' as level_type
      FROM o_level_subject_continuous_assessments_scores ca
      JOIN o_level_subjects sub ON ca.subject_id = sub.id
      WHERE ca.student_id = ? AND ca.term_id = ?
      GROUP BY sub.id, sub.name, sub.short_name
      ORDER BY sub.name
    `;

    // Get student A-Level assessments for the term
    const aLevelAssessmentsQuery = `
      SELECT
        sub.name as subject_name,
        sub.short_name as subject_short_name,
        COUNT(ca.id) as total_assessments,
        AVG(ca.competency_score) as average_score,
        MAX(ca.competency_score) as highest_score,
        MIN(ca.competency_score) as lowest_score,
        'A-Level' as level_type
      FROM a_level_paper_continuous_assessments_scores ca
      JOIN a_level_subject_papers sp ON ca.subject_paper_id = sp.id
      JOIN a_level_subjects sub ON sp.subject_id = sub.id
      WHERE ca.student_id = ? AND ca.term_id = ?
      GROUP BY sub.id, sub.name, sub.short_name
      ORDER BY sub.name
    `;
    
    // Execute both queries and combine results
    const [oLevelResult, aLevelResult] = await Promise.all([
      executeQuery(oLevelAssessmentsQuery, [studentId, term]),
      executeQuery(aLevelAssessmentsQuery, [studentId, term])
    ]);

    const allAssessments = [];
    if (oLevelResult.success) allAssessments.push(...oLevelResult.data);
    if (aLevelResult.success) allAssessments.push(...aLevelResult.data);

    const reportCard = {
      student: studentResult.data[0],
      assessments: allAssessments,
      generated_at: new Date().toISOString()
    };

    res.json({
      success: true,
      data: reportCard
    });

  } catch (error) {
    console.error('Get student report card error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve student report card'
    });
  }
});

// Get student progress report
router.get('/student/:studentId/progress', async (req, res) => {
  try {
    const { studentId } = req.params;

    // O-Level progress query
    const oLevelProgressQuery = `
      SELECT
        t.name as term_name,
        t.number as term_number,
        sub.name as subject_name,
        COUNT(ca.id) as total_assessments,
        AVG(ca.competency_score) as average_score,
        MAX(ca.competency_score) as highest_score,
        MIN(ca.competency_score) as lowest_score,
        'O-Level' as level_type
      FROM o_level_subject_continuous_assessments_scores ca
      JOIN terms t ON ca.term_id = t.id
      JOIN o_level_subjects sub ON ca.subject_id = sub.id
      WHERE ca.student_id = ?
      GROUP BY t.id, t.name, t.number, sub.id, sub.name
      ORDER BY t.number, sub.name
    `;

    // A-Level progress query
    const aLevelProgressQuery = `
      SELECT
        t.name as term_name,
        t.number as term_number,
        sub.name as subject_name,
        COUNT(ca.id) as total_assessments,
        AVG(ca.competency_score) as average_score,
        MAX(ca.competency_score) as highest_score,
        MIN(ca.competency_score) as lowest_score,
        'A-Level' as level_type
      FROM a_level_paper_continuous_assessments_scores ca
      JOIN terms t ON ca.term_id = t.id
      JOIN a_level_subject_papers sp ON ca.subject_paper_id = sp.id
      JOIN a_level_subjects sub ON sp.subject_id = sub.id
      WHERE ca.student_id = ?
      GROUP BY t.id, t.name, t.number, sub.id, sub.name
      ORDER BY t.number, sub.name
    `;

    // Execute both queries and combine results
    const [oLevelResult, aLevelResult] = await Promise.all([
      executeQuery(oLevelProgressQuery, [studentId]),
      executeQuery(aLevelProgressQuery, [studentId])
    ]);

    const allProgress = [];
    if (oLevelResult.success) allProgress.push(...oLevelResult.data);
    if (aLevelResult.success) allProgress.push(...aLevelResult.data);

    res.json({
      success: true,
      data: allProgress
    });

  } catch (error) {
    console.error('Get student progress error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve student progress report'
    });
  }
});

// =============================================
// PDF REPORT GENERATION
// =============================================

// Generate single O-Level report card PDF
router.post('/pdf/o-level/single', async (req, res) => {
  try {
    const { reportData, academicContext } = req.body;

    if (!reportData || !academicContext) {
      return res.status(400).json({
        success: false,
        message: 'Report data and academic context are required'
      });
    }

    const pdfGenerator = new PDFOLevelReportGenerator();
    const result = await pdfGenerator.generateOLevelReportCard(reportData, academicContext);

    res.json({
      success: true,
      message: 'PDF report card generated and saved successfully',
      filePath: result.filePath,
      fileName: result.fileName
    });

  } catch (error) {
    console.error('Single PDF generation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate PDF report card'
    });
  }
});

// Generate bulk O-Level report cards PDF
router.post('/pdf/o-level/bulk', async (req, res) => {
  try {
    console.log('📥 Received bulk PDF generation request');
    const { reportsData, academicContext, totalStudentsInClass } = req.body;

    console.log('📊 Reports data length:', reportsData?.length);
    console.log('👥 Total students in class:', totalStudentsInClass);
    console.log('🎓 Academic context:', academicContext);

    if (!reportsData || !Array.isArray(reportsData) || reportsData.length === 0) {
      console.log('❌ Invalid reports data');
      return res.status(400).json({
        success: false,
        message: 'Reports data array is required'
      });
    }

    if (!academicContext) {
      console.log('❌ Missing academic context');
      return res.status(400).json({
        success: false,
        message: 'Academic context is required'
      });
    }

    // Determine if we should create a folder (only if ALL students are selected)
    const shouldCreateFolder = totalStudentsInClass && reportsData.length === totalStudentsInClass;

    console.log(`📁 Selected ${reportsData.length} out of ${totalStudentsInClass} students. Create folder: ${shouldCreateFolder}`);

    console.log('🏭 Creating PDF generator...');
    const pdfGenerator = new PDFOLevelReportGenerator();

    console.log('🚀 Starting PDF generation...');
    const result = await pdfGenerator.generateBulkOLevelReportCards(reportsData, academicContext, shouldCreateFolder);

    res.json({
      success: true,
      message: shouldCreateFolder
        ? 'Bulk PDF report cards generated and saved in folder successfully'
        : 'Individual PDF report cards generated and saved successfully',
      folderPath: result.folderPath,
      folderName: result.folderName,
      filesGenerated: result.filesGenerated,
      files: result.files,
      createdFolder: shouldCreateFolder
    });

  } catch (error) {
    console.error('Bulk PDF generation error:', error);
    console.error('Error stack:', error.stack);
    console.error('Error message:', error.message);
    res.status(500).json({
      success: false,
      message: `Failed to generate bulk PDF report cards: ${error.message}`
    });
  }
});

// Generate O-Level report cards for specific class/term
router.post('/pdf/o-level/class', async (req, res) => {
  try {
    const { classId, termId, academicYearId, studentIds } = req.body;

    if (!classId || !termId || !academicYearId) {
      return res.status(400).json({
        success: false,
        message: 'Class ID, Term ID, and Academic Year ID are required'
      });
    }

    // Get students for the class if not provided
    let studentsToProcess = studentIds;
    if (!studentsToProcess || studentsToProcess.length === 0) {
      const studentsQuery = `
        SELECT DISTINCT s.id
        FROM o_level_students s
        WHERE s.current_class_id = ?
          AND s.current_term_id = ?
          AND s.current_academic_year_id = ?
          AND s.status = 'active'
      `;

      const studentsResult = await executeQuery(studentsQuery, [classId, termId, academicYearId]);
      if (!studentsResult.success || studentsResult.data.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'No students found for the specified criteria'
        });
      }

      studentsToProcess = studentsResult.data.map(row => row.id);
    }

    // Generate report data for each student
    const reportsData = [];
    for (const studentId of studentsToProcess) {
      try {
        // Use existing report card endpoint logic to get student data
        const reportResponse = await fetch(`${req.protocol}://${req.get('host')}/api/o-level-reports/student/${studentId}/report-card?term=${termId}`, {
          headers: {
            'Authorization': req.headers.authorization
          }
        });

        if (reportResponse.ok) {
          const reportData = await reportResponse.json();
          if (reportData.success) {
            reportsData.push(reportData.data);
          }
        }
      } catch (error) {
        console.error(`Failed to get report data for student ${studentId}:`, error);
      }
    }

    if (reportsData.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'No report data could be generated for the selected students'
      });
    }

    const pdfGenerator = new PDFOLevelReportGenerator();
    const pdfBuffer = await pdfGenerator.generateBulkOLevelReportCards(reportsData);

    // Set headers for PDF download
    const filename = `class-report-cards-${classId}-term-${termId}-${new Date().toISOString().split('T')[0]}.pdf`;
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Length', pdfBuffer.length);

    res.send(pdfBuffer);

  } catch (error) {
    console.error('Class PDF generation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate class report cards PDF'
    });
  }
});

module.exports = router;

// SmartReport Admin Management Components
// Comprehensive system administrator management system with registration and management

// Uses global API services: window.SystemUsersAPI
// Uses global config: window.SRConfig
// Uses environment configuration: ../config/environment.js


// Register Admin Component
const RegisterSystemUserComponent = {
  // Show success message
  showSuccess(message) {
    if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
      window.SRDesignSystem.notifications.show(message, 'success');
    }
  },

  // Show error message
  showError(message) {
    if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
      window.SRDesignSystem.notifications.show(message, 'error');
    }
  },

  // Render register admin form
  render() {
    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'System Admin Registration',
          'Add a new system administrator to manage the system'
        )}

        <!-- Registration Form -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">

          <form id="register-admin-form" class="${SRDesignSystem.responsive.spacing.padding} space-y-8">
            <!-- Personal Information Section -->
            <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900 mb-6">Personal Information</h3>

              <!-- Profile Photo and Phone Number - First row (3 columns: 2+1) -->
              <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gap} mb-4">
                <div class="col-span-2 form-field">
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    Profile Photo
                  </label>
                  <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex items-center space-x-4">
                      <div class="flex-shrink-0">
                        <img id="profile-photo-preview"
                             class="h-20 w-20 rounded-full object-cover border-2 border-gray-200"
                             src="${window.SR.serverUrl}/assets/images/default-avatar.png"
                             alt="Profile Photo"
                             onerror="this.src='${window.SR.serverUrl}/assets/images/default-avatar.png'; this.onerror=null;">
                      </div>
                      <div class="flex-1">
                        <input type="file" id="profile_picture" name="profile_picture" accept="image/*"
                               onchange="RegisterSystemUserComponent.previewPhoto(this)"
                               class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                        <p class="mt-1 text-xs text-gray-500">Upload profile photo (JPG, PNG - Max 2MB)</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-span-1">
                  ${SRDesignSystem.forms.input('phone_number', 'Phone Number', '', {
                    type: 'tel',
                    placeholder: 'e.g., +256752123456 or 0752123456',
                    pattern: '^(\\+256|0)(7[0-9]{8}|3[0-9]{8}|4[0-9]{8})$',
                  })}
                </div>
              </div>

              <!-- Names - Second row (3 columns) -->
              <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gap} mb-4">
                ${SRDesignSystem.forms.input('first_name', 'First Name', '', {
                  required: true,
                  placeholder: 'Enter first name'
                })}
                ${SRDesignSystem.forms.input('middle_name', 'Middle Name', '', {
                  placeholder: 'Enter middle name (optional)'
                })}
                ${SRDesignSystem.forms.input('last_name', 'Last Name', '', {
                  required: true,
                  placeholder: 'Enter last name'
                })}
              </div>

              <!-- Gender and Date of Birth - Third row (2 columns) -->
              <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
                ${SRDesignSystem.forms.select('gender', 'Gender', [
                  { value: '', label: 'Select Gender' },
                  { value: 'Male', label: 'Male' },
                  { value: 'Female', label: 'Female' }
                ], '')}
                ${SRDesignSystem.forms.input('date_of_birth', 'Date of Birth', '', {
                  type: 'date'
                })}
              </div>
            </div>

            <!-- Account Information Section -->
            <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900 mb-6">Account Information</h3>

              <!-- Username and Email Address - Same row (2 columns) -->
              <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap} mb-4">
                ${SRDesignSystem.forms.input('username', 'Username', '', {
                  required: true,
                  placeholder: 'Enter unique username',
                  minlength: 3,
                  pattern: '^[^\\s]+$',
                })}

                ${SRDesignSystem.forms.input('email', 'Email Address', '', {
                  type: 'email',
                  required: true,
                  placeholder: 'Enter email address'
                })}
              </div>

              <!-- Hidden role field - all new users are system_admin -->
              <input type="hidden" id="role" name="role" value="system_admin">

              <!-- Password fields - Same row (2 columns) -->
              <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
                ${SRDesignSystem.forms.input('password', 'Password', '', {
                  type: 'password',
                  required: true,
                  placeholder: 'Enter secure password',
                  minlength: 8,
                })}

                ${SRDesignSystem.forms.input('confirm_password', 'Confirm Password', '', {
                  type: 'password',
                  required: true,
                  placeholder: 'Confirm password'
                })}
              </div>
            </div>



            <!-- Form Actions -->
            <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
              <div class="flex items-center justify-end">
                ${SRDesignSystem.forms.button('register', 'Register System Admin', 'primary', {
                  type: 'submit',
                  icon: 'fas fa-user-plus'
                })}
              </div>
            </div>
          </form>
        </div>

      </div>
    `;
  },

  // Initialize register admin component
  async init() {
    console.log('🔧 Initializing RegisterSystemUserComponent...');

    // Reset component state
    this.resetComponentState();


    console.log('🔄 Initializing RegisterSystemUserComponent functionality...');
    this.initializeEventListeners();
    console.log('✅ RegisterSystemUserComponent initialized successfully');
  },

  // Reset component state
  resetComponentState() {
    // Reset any component-specific state if needed
    console.log('🔄 RegisterSystemUserComponent state reset');
  },

  // Cleanup component
  cleanup() {
    console.log('🧹 Cleaning up RegisterSystemUserComponent...');
    this.resetComponentState();

    // Reset form if it exists
    const form = document.getElementById('register-admin-form');
    if (form) {
      form.reset();
    }

    console.log('✅ RegisterSystemUserComponent cleanup completed');
  },



  // Initialize event listeners
  initializeEventListeners() {
    // Handle form submission
    const form = document.getElementById('register-admin-form');
    if (form) {
      form.addEventListener('submit', this.handleSubmit.bind(this));
    }

    // Setup password validation with real-time feedback
    SRDesignSystem.password.setupValidation('password', 'confirm_password');

    // Setup name field formatting
    this.setupNameFieldFormatting();

    // Setup phone number validation
    this.setupPhoneNumberValidation();

    // Setup username validation
    this.setupUsernameValidation();
  },

  // Setup name field formatting (uppercase, letters only)
  setupNameFieldFormatting() {
    const nameFields = ['first_name', 'middle_name', 'last_name'];

    nameFields.forEach(fieldId => {
      const field = document.getElementById(fieldId);
      if (field) {
        // Prevent non-letter characters from being typed
        field.addEventListener('keydown', (e) => {
          // Allow navigation and control keys
          const allowedKeys = ['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'];

          // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+Z
          if ((e.ctrlKey && ['a', 'c', 'v', 'x', 'z'].includes(e.key.toLowerCase())) ||
              allowedKeys.includes(e.key)) {
            return;
          }

          // Prevent spaces and only allow letters (a-z, A-Z)
          if (e.key === ' ' || !/^[a-zA-Z]$/.test(e.key)) {
            e.preventDefault();
          }
        });

        // Convert to uppercase and remove any non-letter characters including spaces
        field.addEventListener('input', (e) => {
          let value = e.target.value.toUpperCase().replace(/[^A-Z]/g, '');
          e.target.value = value;
        });

        // Handle paste events - only allow letters, no spaces
        field.addEventListener('paste', (e) => {
          e.preventDefault();
          let paste = '';
          if (e.clipboardData) {
            paste = e.clipboardData.getData('text');
          }
          paste = paste.toUpperCase().replace(/[^A-Z]/g, '');
          e.target.value = paste;
        });
      }
    });
  },

  // Setup phone number validation
  setupPhoneNumberValidation() {
    const phoneField = document.getElementById('phone_number');
    if (phoneField) {
      // Allow only digits, +, and spaces during input
      phoneField.addEventListener('input', (e) => {
        let value = e.target.value;
        // Remove any characters that aren't digits, +, or spaces
        value = value.replace(/[^\d\+\s]/g, '');
        e.target.value = value;

        // Validate against the pattern
        const pattern = /^(\+256|0)(7[0-9]{8}|3[0-9]{8}|4[0-9]{8})$/;
        if (value && !pattern.test(value.replace(/\s/g, ''))) {
          e.target.setCustomValidity('Enter a valid Ugandan phone number');
        } else {
          e.target.setCustomValidity('');
        }
      });

      // Format on blur (remove spaces for validation)
      phoneField.addEventListener('blur', (e) => {
        let value = e.target.value.replace(/\s/g, ''); // Remove spaces
        e.target.value = value;
      });
    }
  },

  // Setup username validation (no spaces, minimum 3 characters)
  setupUsernameValidation() {
    const usernameField = document.getElementById('username');
    if (usernameField) {
      // Prevent spaces from being typed
      usernameField.addEventListener('keydown', (e) => {
        if (e.key === ' ') {
          e.preventDefault();
        }
      });

      usernameField.addEventListener('input', (e) => {
        let value = e.target.value;
        // Remove spaces
        value = value.replace(/\s/g, '');
        e.target.value = value;

        // Validate length and format
        if (value.length < 3) {
          e.target.setCustomValidity('Username must be at least 3 characters long');
        } else {
          e.target.setCustomValidity('');
        }
      });
    }
  },

  // Preview profile photo
  previewPhoto(input) {
    if (input.files && input.files[0]) {
      const file = input.files[0];

      // Validate file size (2MB max)
      if (file.size > 2 * 1024 * 1024) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('File size must be less than 2MB', 'error');
        } else {
          alert('File size must be less than 2MB');
        }
        input.value = '';
        return;
      }

      // Validate file type (only JPG and PNG)
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      if (!allowedTypes.includes(file.type)) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Please select a JPG or PNG image file', 'error');
        } else {
          alert('Please select a JPG or PNG image file');
        }
        input.value = '';
        return;
      }

      const reader = new FileReader();
      reader.onload = function(e) {
        const preview = document.getElementById('profile-photo-preview');
        if (preview) {
          preview.src = e.target.result;
        }
      };
      reader.readAsDataURL(file);
    }
  },

  // Handle form submission
  async handleSubmit(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    let data = Object.fromEntries(formData.entries());

    // Format name fields (uppercase, letters only)
    if (data.first_name) data.first_name = data.first_name.toUpperCase().replace(/[^A-Z]/g, '');
    if (data.middle_name) data.middle_name = data.middle_name.toUpperCase().replace(/[^A-Z]/g, '');
    if (data.last_name) data.last_name = data.last_name.toUpperCase().replace(/[^A-Z]/g, '');

    // Validate password confirmation
    if (data.password !== data.confirm_password) {
      this.showError('Passwords do not match');
      return;
    }

    // Validate password strength using design system
    const passwordValidation = SRDesignSystem.password.validate(data.password);
    if (!passwordValidation.isValid) {
      this.showError('Password does not meet security requirements. Please check the password strength indicator.');
      return;
    }

    // Remove confirm_password from data
    delete data.confirm_password;

    // Validate required fields
    if (!data.first_name || !data.last_name || !data.username || !data.email || !data.password || !data.role) {
      this.showError('Please fill in all required fields including administrator role');
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
      this.showError('Please enter a valid email address');
      return;
    }

    // Validate username (no spaces, minimum 3 characters)
    if (data.username.length < 3 || /\s/.test(data.username)) {
      this.showError('Username must be at least 3 characters long and contain no spaces');
      return;
    }

    // Handle profile photo upload if provided
    const profilePhotoFile = formData.get('profile_picture');
    if (profilePhotoFile && profilePhotoFile.size > 0) {
      try {
        // Upload the photo first
        const uploadResult = await window.ImageUploadUtil.uploadImage(
          profilePhotoFile,
          'system-user-photo'
        );

        if (uploadResult.success) {
          data.profile_picture = uploadResult.filePath;
        } else {
          throw new Error('Failed to upload profile photo');
        }
      } catch (uploadError) {
        console.error('Photo upload error:', uploadError);
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(
            'Failed to upload profile photo: ' + uploadError.message,
            'error'
          );
        }
        SRDesignSystem.forms.setButtonLoading('register', false);
        return;
      }
    } else {
      // No photo provided, set to null
      data.profile_picture = null;
    }

    try {
      SRDesignSystem.forms.setButtonLoading('register', true);

      console.log('📝 Registering admin with data:', {
        username: data.username,
        email: data.email,
        first_name: data.first_name,
        last_name: data.last_name,
        hasPassword: !!data.password
      });

      // Use the API service
      const result = await window.SystemUsersAPI.create(data);

      console.log('📋 Registration result:', result);

      if (result.success) {
        this.showSuccess('New system administrator registered successfully! They can now log in with their credentials.');
        event.target.reset();
        // Redirect to manage admins
        setTimeout(() => {
          if (window.PageRouter) {
            window.PageRouter.loadPage('manage-admins');
          }
        }, 1500);
      } else {
        this.showError(result.message || 'Failed to register administrator');
      }
    } catch (error) {
      console.error('Registration error:', error);

      // Handle different types of errors
      let errorMessage = 'Failed to register administrator';

      if (error.message) {
        if (error.message.includes('Username or email already exists')) {
          errorMessage = 'Username or email already exists. Please choose different credentials.';
        } else if (error.message.includes('Academic context not set')) {
          errorMessage = 'Academic context not set. Please set up an active academic year and term before registering administrators.';
        } else if (error.message.includes('setupRequired')) {
          errorMessage = 'Academic setup required. Please configure academic year and term settings first.';
        } else {
          errorMessage = error.message;
        }
      }

      this.showError(errorMessage);
    } finally {
      SRDesignSystem.forms.setButtonLoading('register', false);
    }
  },


};

// Manage Admins Component
const ManageSystemUserComponent = {
  // Component state
  state: {
    systemUsers: [],
    loading: false,
    filters: {
      status: '',
      search: ''
    }
  },

  // Show success message
  showSuccess(message) {
    if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
      window.SRDesignSystem.notifications.show(message, 'success');
    }
  },

  // Show error message
  showError(message) {
    if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
      window.SRDesignSystem.notifications.show(message, 'error');
    }
  },

  // Load system users data
  async loadSystemUsersData() {
    try {
      this.state.loading = true;
      console.log('🔄 Loading system users data...');

      // Load system users (admins)
      const usersResponse = await window.SystemUsersAPI.getAll();
      if (usersResponse && usersResponse.success) {
        this.state.systemUsers = usersResponse.data || [];
        console.log('✅ System users loaded:', this.state.systemUsers.length, 'administrators');
      } else {
        console.warn('⚠️ Failed to load system users');
        throw new Error('Failed to load system users data');
      }

      console.log('✅ System users data loaded successfully');

    } catch (error) {
      console.error('❌ Error loading system users data:', error);
      this.showError('Failed to load admin data');
      throw error;
    } finally {
      this.state.loading = false;
    }
  },

  // Render manage admins interface
  render() {
    // Show loading state if data is still loading
    if (this.state.loading) {
      // Use centralized loading state from page router
      const container = document.getElementById('content-area');
      if (container && window.PageRouter) {
        window.PageRouter.showLoadingState(container, 'Loading admin management data...');
      }
      return '';
    }

    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'System Admin Management',
          'View, edit, and manage system administrator accounts'
        )}

        <!-- Admin Management Actions -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <div class="flex items-center justify-end mb-6">
            <div class="flex items-center space-x-3">
              ${SR.currentUser && SR.currentUser.role === 'super_user' ?
                SRDesignSystem.forms.button('add-admin', 'Register System Admin', 'primary', {
                  icon: 'fas fa-user-plus',
                  onclick: 'ManageSystemUserComponent.addAdmin()'
                }) :
                `<div class="${SRDesignSystem.responsive.text.sm} text-gray-500">${SRDesignSystem.components.icon('fas fa-info-circle', 'sm', 'current')} <span class="ml-2">Only Super Users can register new system administrators</span></div>`
              }
            </div>
          </div>

          <!-- Filters and Search -->
          <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap} mb-6">
            ${SRDesignSystem.forms.input('search', 'Search Administrators', '', {
              placeholder: 'Search by name, username, or email...',
              icon: 'fas fa-search'
            })}
            ${SRDesignSystem.forms.select('filter_status', 'Filter by Status', [
              { value: '', label: 'All Status' },
              { value: 'true', label: 'Active' },
              { value: 'false', label: 'Inactive' }
            ], '')}
          </div>
        </div>

        <!-- Admins Table -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div class="bg-gradient-to-r from-primary-50 to-primary-100 px-6 py-4 border-b border-primary-200">
            <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-primary-900 flex items-center">
              ${SRDesignSystem.components.icon('fas fa-users-cog', 'base', 'primary-600')}
              <span class="ml-3">System Administrators</span>
            </h3>
          </div>
          <div class="overflow-x-auto max-h-96 overflow-y-auto">
            <table class="${SRDesignSystem.responsive.table.base}">
              <thead class="bg-gray-50 sticky top-0 z-10">
                <tr>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Photo</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Name</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Role</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Status</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-right ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Actions</th>
                </tr>
              </thead>
              <tbody id="admins-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Admins will be populated here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    `;
  },

  // Initialize manage admins component
  async init() {
    console.log('🔧 Initializing ManageSystemUserComponent...');

    // Reset component state
    this.resetComponentState();

    // Load system users data (requires super user privileges)
    try {
      await this.loadSystemUsersData();
    } catch (error) {
      console.error('❌ Failed to load system users data:', error);
      this.showError('Failed to load admin data. Please check your permissions.');
      return; // Don't continue initialization if data loading fails
    }

    console.log('🔄 Populating ManageSystemUserComponent UI...');
    this.populateAdminsTable();
    this.initializeEventListeners();
    console.log('✅ ManageSystemUserComponent initialized successfully');
  },

  // Reset component state
  resetComponentState() {
    // Reset any component-specific state if needed
    console.log('🔄 ManageSystemUserComponent state reset');
  },

  // Cleanup component
  cleanup() {
    console.log('🧹 Cleaning up ManageSystemUserComponent...');
    this.resetComponentState();

    // Hide any open modals
    const modals = ['edit-admin-modal', 'delete-admin-modal'];
    modals.forEach(modalId => {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.classList.add('hidden');
      }
    });

    console.log('✅ ManageSystemUserComponent cleanup completed');
  },



  // Populate admins table
  populateAdminsTable() {
    console.log('🔄 Populating admins table...');
    const tableBody = document.getElementById('admins-table-body');
    if (!tableBody) {
      console.error('❌ Admins table body not found');
      return;
    }

    const admins = this.getFilteredAdmins();
    console.log('📋 Filtered admins:', admins.length);

    if (admins.length === 0) {
      // Get current filter values to provide contextual messaging
      const statusFilter = document.getElementById('filter_status')?.selectedOptions[0]?.text || '';
      const searchTerm = document.getElementById('search')?.value || '';

      let message = 'No administrators found';
      let suggestion = 'Try adjusting your search criteria or add new administrators.';

      // Provide specific messaging based on applied filters
      if (searchTerm) {
        message = `No administrators found matching "${searchTerm}"`;
        suggestion = `Try a different search term or check the spelling.`;
      } else if (statusFilter && statusFilter !== 'All Status') {
        message = `No ${statusFilter.toLowerCase()} administrators found`;
        suggestion = `Try selecting a different status or check if administrators exist with this status.`;
      }

      tableBody.innerHTML = `
        <tr>
          <td colspan="5" class="px-6 py-12 text-center text-gray-500">
            <div class="text-gray-400 mb-4">
              ${SRDesignSystem.components.icon('fas fa-user-shield', '4xl', 'gray-300')}
            </div>
            <p class="${SRDesignSystem.responsive.text.lg} font-medium">${message}</p>
            <p class="${SRDesignSystem.responsive.text.sm}">${suggestion}</p>
          </td>
        </tr>
      `;
      return;
    }

    tableBody.innerHTML = admins.map(admin => this.renderAdminRow(admin)).join('');
    console.log('✅ Admins table populated successfully');
  },

  // Helper method to get proper admin photo URL with fallback
  getAdminPhotoUrl(profilePicture) {
    const serverUrl = window.SRConfig.getServerUrl();

    // Handle null, undefined, empty string, or string "null"
    if (profilePicture === null ||
        profilePicture === undefined ||
        profilePicture === '' ||
        profilePicture === 'null' ||
        profilePicture === 'undefined' ||
        (typeof profilePicture === 'string' && profilePicture.trim() === '')) {
      return `${serverUrl}/assets/images/default-avatar.png`;
    }

    // Convert to string and trim
    const photoPath = String(profilePicture).trim();

    // If photo path starts with 'C:' or contains backslashes, it's an invalid path
    if (photoPath.startsWith('C:') || photoPath.includes('\\') || photoPath.startsWith('file://')) {
      return `${serverUrl}/assets/images/default-avatar.png`;
    }

    // If photo path doesn't start with '/', add it
    if (!photoPath.startsWith('/')) {
      return `${serverUrl}/${photoPath}`;
    }

    return `${serverUrl}${photoPath}`;
  },

  // Render individual admin row
  renderAdminRow(admin) {
    const fullName = [admin.first_name, admin.middle_name, admin.last_name].filter(Boolean).join(' ');
    const isActive = admin.is_active;
    const isSuperUser = admin.role === 'super_user';

    // Define dropdown actions - only View Details (following ManageTeachersComponent pattern)
    const actions = [
      {
        label: 'View Details',
        icon: 'fas fa-eye',
        onclick: `ManageSystemUserComponent.viewAdmin(${admin.id})`,
        color: 'blue'
      }
    ];

    return `
      <tr class="hover:bg-gray-50">
        <td class="${SRDesignSystem.responsive.table.cell}">
          <div class="flex items-center justify-center">
            <div class="flex-shrink-0 h-10 w-10">
              <img class="h-10 w-10 rounded-full object-cover border-2 border-gray-200"
                   src="${ManageSystemUserComponent.getAdminPhotoUrl(admin.profile_picture)}"
                   alt="${fullName}"
                   onerror="this.src='${window.SR.serverUrl}/assets/images/default-avatar.png'; this.onerror=null;">
            </div>
          </div>
        </td>
        <td class="${SRDesignSystem.responsive.table.cell}">
          <div class="${SRDesignSystem.responsive.text.sm} font-medium text-gray-900">${fullName}</div>
        </td>
        <td class="${SRDesignSystem.responsive.table.cell}">
          ${SRDesignSystem.components.badge(
            isSuperUser ? 'Super User' : 'System Admin',
            isSuperUser ? 'purple' : 'blue'
          )}
        </td>
        <td class="${SRDesignSystem.responsive.table.cell}">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full ${SRDesignSystem.responsive.text.xs} font-medium ${
            isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }">
            <span class="w-1.5 h-1.5 mr-1.5 rounded-full ${isActive ? 'bg-green-400' : 'bg-red-400'}"></span>
            ${isActive ? 'Active' : 'Inactive'}
          </span>
        </td>
        <td class="${SRDesignSystem.responsive.table.cell} text-right">
          ${SRDesignSystem.tables.generateKebabMenu(`admin-${admin.id}`, actions, admin)}
        </td>
      </tr>
    `;
  },



  // Get filtered admins based on search and status
  getFilteredAdmins() {
    let admins = this.state.systemUsers || [];

    // Apply search filter
    const searchTerm = document.getElementById('search')?.value?.toLowerCase() || '';
    if (searchTerm) {
      admins = admins.filter(admin => {
        const fullName = [admin.first_name, admin.middle_name, admin.last_name].filter(Boolean).join(' ').toLowerCase();
        return fullName.includes(searchTerm) ||
               admin.username.toLowerCase().includes(searchTerm) ||
               admin.email.toLowerCase().includes(searchTerm);
      });
    }

    // Apply status filter
    const statusFilter = document.getElementById('filter_status')?.value;
    if (statusFilter !== '') {
      const isActive = statusFilter === 'true';
      admins = admins.filter(admin => admin.is_active === isActive);
    }

    return admins;
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Search input with debounce for better performance
    const searchInput = document.getElementById('search');
    if (searchInput) {
      const debouncedSearch = window.ComponentLifecycleManager?.debounce(() => {
        this.populateAdminsTable();
      }, 300) || (() => this.populateAdminsTable());

      searchInput.addEventListener('input', debouncedSearch);
    }

    // Status filter
    const statusFilter = document.getElementById('filter_status');
    if (statusFilter) {
      statusFilter.addEventListener('change', () => {
        this.populateAdminsTable();
      });
    }
  },

  // Add new admin
  addAdmin() {
    if (window.PageRouter) {
      window.PageRouter.loadPage('register-admin');
    }
  },



  // View admin details
  async viewAdmin(adminId) {
    try {
      const admin = this.state.systemUsers.find(a => a.id === adminId);
      if (!admin) {
        this.showError('Administrator not found');
        return;
      }

      this.showAdminDetailsModal(admin);
    } catch (error) {
      console.error('Error viewing admin:', error);
      this.showError('Failed to load administrator details');
    }
  },

  // Edit admin
  async editAdmin(adminId) {
    try {
      const admin = this.state.systemUsers.find(a => a.id === adminId);
      if (!admin) {
        this.showError('Administrator not found');
        return;
      }

      this.showEditAdminModal(admin);
    } catch (error) {
      console.error('Error editing admin:', error);
      this.showError('Failed to load administrator for editing');
    }
  },

  // Activate admin
  async activateAdmin(adminId) {
    try {
      const result = await window.SystemUsersAPI.update(adminId, { is_active: true });
      if (result.success) {
        this.showSuccess('Administrator activated successfully');
        await this.loadSystemUsersData();
        this.populateAdminsTable();
      } else {
        this.showError(result.message || 'Failed to activate administrator');
      }
    } catch (error) {
      console.error('Error activating admin:', error);
      this.showError('Failed to activate administrator');
    }
  },

  // Deactivate admin
  async deactivateAdmin(adminId) {
    if (confirm('Are you sure you want to deactivate this administrator?')) {
      try {
        const result = await window.SystemUsersAPI.update(adminId, { is_active: false });
        if (result.success) {
          this.showSuccess('Administrator deactivated successfully');
          await this.loadSystemUsersData();
          this.populateAdminsTable();
        } else {
          this.showError(result.message || 'Failed to deactivate administrator');
        }
      } catch (error) {
        console.error('Error deactivating admin:', error);
        this.showError('Failed to deactivate administrator');
      }
    }
  },

  // Confirm delete admin with modal
  confirmDeleteAdmin(adminId, adminName) {
    const confirmationHtml = `
      <div id="delete-admin-confirmation" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
          <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
              ${SRDesignSystem.components.icon('fas fa-exclamation-triangle', 'xl', 'red-600')}
            </div>
            <h3 class="${SRDesignSystem.responsive.text.lg} font-medium text-gray-900 mt-4">Delete Administrator</h3>
            <div class="mt-2 px-7 py-3">
              <p class="${SRDesignSystem.responsive.text.sm} text-gray-500">
                Are you sure you want to delete <strong>${adminName}</strong>?
                This action cannot be undone and will remove all associated data.
              </p>
            </div>
            <div class="flex justify-center space-x-3 px-4 py-3">
              ${SRDesignSystem.forms.button('confirm-delete-admin', 'Delete', 'red', {
                icon: 'fas fa-trash',
                onclick: `ManageSystemUserComponent.deleteAdmin(${adminId}); this.closest('#delete-admin-confirmation').remove();`
              })}
              ${SRDesignSystem.forms.button('cancel-delete-admin', 'Cancel', 'secondary', {
                onclick: 'this.closest(\'#delete-admin-confirmation\').remove()'
              })}
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', confirmationHtml);
  },

  // Delete admin
  async deleteAdmin(adminId) {
    try {
      const result = await window.SystemUsersAPI.delete(adminId);
      if (result.success) {
        this.showSuccess('Administrator deleted successfully');
        await this.loadSystemUsersData();
        this.populateAdminsTable();
      } else {
        this.showError(result.message || 'Failed to delete administrator');
      }
    } catch (error) {
      console.error('Error deleting admin:', error);
      this.showError('Failed to delete administrator');
    }
  },

  // Show admin details modal
  showAdminDetailsModal(admin) {
    const fullName = [admin.first_name, admin.middle_name, admin.last_name].filter(Boolean).join(' ');
    const currentUser = SR.currentUser;
    const canManageAdmin = currentUser && currentUser.role === 'super_user';

    const modalHtml = `
      <div id="admin-details-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-3/4 max-w-4xl shadow-lg rounded-md bg-white">
          <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between pb-4 border-b">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900">Administrator Details</h3>
              <button onclick="this.closest('#admin-details-modal').remove()"
                      class="text-gray-400 hover:text-gray-600 p-1">
                ${SRDesignSystem.components.icon('fas fa-times', 'lg', 'current')}
              </button>
            </div>

            <!-- Admin Details -->
            <div class="py-6">
              <!-- Personal Information -->
              <div class="bg-white rounded-lg p-6">
                <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
                  <!-- Photo -->
                  <div class="col-span-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Photo</label>
                    <div class="flex items-center">
                      <img class="h-16 w-16 rounded-full object-cover border-2 border-gray-200"
                           src="${ManageSystemUserComponent.getAdminPhotoUrl(admin.profile_picture)}"
                           alt="${fullName}"
                           onerror="this.src='${window.SR.serverUrl}/assets/images/default-avatar.png'; this.onerror=null;">
                    </div>
                  </div>
                  <!-- Full Name -->
                  <div class="col-span-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                    <p class="text-sm text-gray-900">${fullName}</p>
                  </div>
                  <!-- Date of Birth -->
                  <div class="col-span-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Date of Birth</label>
                    <p class="text-sm text-gray-900">${admin.date_of_birth ? new Date(admin.date_of_birth).toLocaleDateString() : 'Not specified'}</p>
                  </div>
                  <!-- Gender -->
                  <div class="col-span-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Gender</label>
                    <p class="text-sm text-gray-900">${admin.gender || 'Not specified'}</p>
                  </div>
                  <!-- Email -->
                  <div class="col-span-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                    <p class="text-sm text-gray-900">${admin.email}</p>
                  </div>
                  <!-- Phone Number -->
                  <div class="col-span-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                    <p class="text-sm text-gray-900">${admin.phone_number || 'Not specified'}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end pt-4 border-t">
              <div class="flex space-x-3">
                ${canManageAdmin ? SRDesignSystem.forms.button('edit-admin-details', 'Edit Details', 'blue', {
                  icon: 'fas fa-edit',
                  onclick: `ManageSystemUserComponent.showEditAdminModal(${JSON.stringify(admin).replace(/"/g, '&quot;')}); this.closest('#admin-details-modal').remove();`
                }) : `<div class="${SRDesignSystem.responsive.text.sm} text-gray-500">Only Super Users can edit administrator details</div>`}
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
  },

  // Show edit admin modal
  showEditAdminModal(admin) {
    const currentUser = SR.currentUser;
    const canChangePassword = currentUser && currentUser.role === 'super_user';

    const modalHtml = `
      <div id="edit-admin-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-10 mx-auto p-5 border w-2/3 shadow-lg rounded-md bg-white">
          <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between pb-4 border-b">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900">Edit Administrator</h3>
              <button onclick="this.closest('#edit-admin-modal').remove()"
                      class="text-gray-400 hover:text-gray-600">
                ${SRDesignSystem.components.icon('fas fa-times', 'xl', 'current')}
              </button>
            </div>

            <!-- Edit Form -->
            <form id="edit-admin-form" class="py-4 space-y-6">
              <input type="hidden" id="edit-admin-id" value="${admin.id}">

              <!-- Profile Photo Section -->
              <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-sm font-medium text-gray-900 mb-3">Profile Photo</h4>
                <div class="flex items-center space-x-4">
                  <div class="flex-shrink-0">
                    <img id="edit-admin-photo-preview"
                         class="h-20 w-20 rounded-full object-cover border-2 border-gray-200"
                         src="${ManageSystemUserComponent.getAdminPhotoUrl(admin.profile_picture)}"
                         alt="Admin Photo"
                         onerror="this.src='${window.SR.serverUrl}/assets/images/default-avatar.png'; this.onerror=null;">
                  </div>
                  <div class="flex-1">
                    <input type="file" id="edit-profile-picture" accept="image/*"
                           onchange="ManageSystemUserComponent.previewEditPhoto(this)"
                           class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                    <p class="mt-1 text-xs text-gray-500">Upload a new profile photo (JPG, PNG - Max 2MB)</p>
                  </div>
                </div>
              </div>

              <!-- Row 1: First Name, Middle Name, Last Name -->
              <div class="grid grid-cols-3 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700">First Name <span class="text-red-500">*</span></label>
                  <input type="text" id="edit-first-name" name="first_name" value="${admin.first_name || ''}"
                         class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
                         style="text-transform: uppercase;" required>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Middle Name</label>
                  <input type="text" id="edit-middle-name" name="middle_name" value="${admin.middle_name || ''}"
                         class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
                         style="text-transform: uppercase;">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Last Name <span class="text-red-500">*</span></label>
                  <input type="text" id="edit-last-name" name="last_name" value="${admin.last_name || ''}"
                         class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
                         style="text-transform: uppercase;" required>
                </div>
              </div>

              <!-- Row 2: Username, Email, Account Status -->
              <div class="grid grid-cols-3 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700">Username <span class="text-red-500">*</span></label>
                  <input type="text" id="edit-username" name="username" value="${admin.username || ''}"
                         class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200" required>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Email Address <span class="text-red-500">*</span></label>
                  <input type="email" id="edit-email" name="email" value="${admin.email || ''}"
                         class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200" required>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Account Status</label>
                  <select id="edit-is-active" name="is_active"
                          class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
                    <option value="true" ${admin.is_active ? 'selected' : ''}>Active</option>
                    <option value="false" ${!admin.is_active ? 'selected' : ''}>Inactive</option>
                  </select>
                </div>
              </div>

              <!-- Row 3: Gender, Date of Birth, Phone Number -->
              <div class="grid grid-cols-3 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700">Gender</label>
                  <select id="edit-gender" name="gender"
                          class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
                    <option value="">Select Gender</option>
                    <option value="Male" ${admin.gender === 'Male' ? 'selected' : ''}>Male</option>
                    <option value="Female" ${admin.gender === 'Female' ? 'selected' : ''}>Female</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Date of Birth</label>
                  <input type="date" id="edit-date-of-birth" name="date_of_birth" value="${admin.date_of_birth ? new Date(admin.date_of_birth).toISOString().split('T')[0] : ''}"
                         class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Phone Number</label>
                  <input type="tel" id="edit-phone-number" name="phone_number" value="${admin.phone_number || ''}"
                         class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
                         placeholder="e.g., +256 700 000 000">
                </div>
              </div>



              ${canChangePassword ? `
              <!-- Password Change Section -->
              <div class="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
                <h4 class="text-sm font-medium text-gray-900 mb-3">Change Password</h4>
                <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
                  <div>
                    <label class="block text-sm font-medium text-gray-700">New Password</label>
                    <input type="password" id="edit-new-password" name="new_password"
                           class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
                           placeholder="Enter new password" minlength="8">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Confirm New Password</label>
                    <input type="password" id="edit-confirm-password" name="confirm_password"
                           class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
                           placeholder="Confirm new password">
                  </div>
                </div>
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-4">
                  <p class="text-sm text-yellow-800">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    Leave password fields empty if you don't want to change the password.
                  </p>
                </div>
              </div>
              ` : `
              <!-- Password Change Restricted -->
              <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <div class="flex items-center">
                  <div class="bg-gray-100 rounded-full p-2 mr-3">
                    <i class="fas fa-lock text-gray-600"></i>
                  </div>
                  <div>
                    <h4 class="text-sm font-medium text-gray-900">Password Management</h4>
                    <p class="text-sm text-gray-600">Only Super Users can change administrator passwords.</p>
                  </div>
                </div>
              </div>
              `}
            </form>

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-3 pt-4 border-t">
              ${SRDesignSystem.forms.button('cancel-edit-admin', 'Cancel', 'secondary', {
                onclick: 'this.closest(\'#edit-admin-modal\').remove()'
              })}
              ${SRDesignSystem.forms.button('save-admin-changes', 'Save Changes', 'blue', {
                icon: 'fas fa-save',
                onclick: 'ManageSystemUserComponent.saveAdminChanges()'
              })}
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Initialize validation for edit form
    this.setupEditFormValidation(canChangePassword);
  },

  // Preview photo in edit modal
  previewEditPhoto(input) {
    if (input.files && input.files[0]) {
      const file = input.files[0];

      // Validate file size (2MB max)
      if (file.size > 2 * 1024 * 1024) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('File size must be less than 2MB', 'error');
        }
        input.value = '';
        return;
      }

      // Validate file type (only JPG and PNG)
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      if (!allowedTypes.includes(file.type)) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Please select a JPG or PNG image file', 'error');
        }
        input.value = '';
        return;
      }

      const reader = new FileReader();
      reader.onload = function(e) {
        const preview = document.getElementById('edit-admin-photo-preview');
        if (preview) {
          preview.src = e.target.result;
        }
      };
      reader.readAsDataURL(file);
    }
  },

  // Setup validation for edit form
  setupEditFormValidation(canChangePassword) {
    // Setup name field formatting (uppercase, letters only)
    const nameFields = ['edit-first-name', 'edit-middle-name', 'edit-last-name'];

    nameFields.forEach(fieldId => {
      const field = document.getElementById(fieldId);
      if (field) {
        // Prevent non-letter characters and spaces from being typed
        field.addEventListener('keydown', (e) => {
          const allowedKeys = ['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'];

          if ((e.ctrlKey && ['a', 'c', 'v', 'x', 'z'].includes(e.key.toLowerCase())) ||
              allowedKeys.includes(e.key)) {
            return;
          }

          // Prevent spaces and only allow letters (a-z, A-Z)
          if (e.key === ' ' || !/^[a-zA-Z]$/.test(e.key)) {
            e.preventDefault();
          }
        });

        // Convert to uppercase and remove any non-letter characters including spaces
        field.addEventListener('input', (e) => {
          let value = e.target.value.toUpperCase().replace(/[^A-Z]/g, '');
          e.target.value = value;
        });

        // Handle paste events - only allow letters, no spaces
        field.addEventListener('paste', (e) => {
          e.preventDefault();
          let paste = '';
          if (e.clipboardData) {
            paste = e.clipboardData.getData('text');
          }
          paste = paste.toUpperCase().replace(/[^A-Z]/g, '');
          e.target.value = paste;
        });
      }
    });

    // Setup username validation (no spaces, minimum 3 characters)
    const usernameField = document.getElementById('edit-username');
    if (usernameField) {
      // Prevent spaces from being typed
      usernameField.addEventListener('keydown', (e) => {
        if (e.key === ' ') {
          e.preventDefault();
        }
      });

      usernameField.addEventListener('input', (e) => {
        let value = e.target.value;
        // Remove spaces
        value = value.replace(/\s/g, '');
        e.target.value = value;

        // Validate length and format
        if (value.length < 3) {
          e.target.setCustomValidity('Username must be at least 3 characters long');
        } else {
          e.target.setCustomValidity('');
        }
      });
    }

    // Setup email validation
    const emailField = document.getElementById('edit-email');
    if (emailField) {
      emailField.addEventListener('input', (e) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (e.target.value && !emailRegex.test(e.target.value)) {
          e.target.setCustomValidity('Please enter a valid email address');
        } else {
          e.target.setCustomValidity('');
        }
      });
    }

    // Setup phone number validation
    const phoneField = document.getElementById('edit-phone-number');
    if (phoneField) {
      phoneField.addEventListener('input', (e) => {
        let value = e.target.value;
        // Remove any characters that aren't digits, +, or spaces
        value = value.replace(/[^\d\+\s]/g, '');
        e.target.value = value;

        // Validate against the pattern if value exists
        if (value) {
          const pattern = /^(\+256|0)(7[0-9]{8}|3[0-9]{8}|4[0-9]{8})$/;
          if (!pattern.test(value.replace(/\s/g, ''))) {
            e.target.setCustomValidity('Enter a valid Ugandan phone number (e.g., +256 700 000 000 or 0700 000 000)');
          } else {
            e.target.setCustomValidity('');
          }
        } else {
          e.target.setCustomValidity('');
        }
      });
    }

    // Setup password validation for edit form (only for super users)
    if (canChangePassword) {
      SRDesignSystem.password.setupValidation('edit-new-password', 'edit-confirm-password');
    }
  },

  // Save admin changes
  async saveAdminChanges() {
    try {
      const form = document.getElementById('edit-admin-form');
      if (!form) return;

      // Get form data using the new field IDs
      const data = {
        first_name: document.getElementById('edit-first-name')?.value?.trim(),
        middle_name: document.getElementById('edit-middle-name')?.value?.trim() || null,
        last_name: document.getElementById('edit-last-name')?.value?.trim(),
        username: document.getElementById('edit-username')?.value?.trim(),
        email: document.getElementById('edit-email')?.value?.trim(),
        gender: document.getElementById('edit-gender')?.value || null,
        date_of_birth: document.getElementById('edit-date-of-birth')?.value || null,
        phone_number: document.getElementById('edit-phone-number')?.value?.trim() || null,
        is_active: document.getElementById('edit-is-active')?.value === 'true'
      };

      // Format name fields (uppercase, letters only)
      if (data.first_name) data.first_name = data.first_name.toUpperCase().replace(/[^A-Z]/g, '');
      if (data.middle_name) data.middle_name = data.middle_name.toUpperCase().replace(/[^A-Z]/g, '');
      if (data.last_name) data.last_name = data.last_name.toUpperCase().replace(/[^A-Z]/g, '');

      // Validate required fields
      if (!data.first_name || !data.last_name || !data.username || !data.email) {
        this.showError('Please fill in all required fields');
        return;
      }

      // Handle photo upload if a new photo was selected
      const photoInput = document.getElementById('edit-profile-picture');
      let photoFormData = null;
      const adminId = document.getElementById('edit-admin-id')?.value;

      if (photoInput && photoInput.files && photoInput.files[0]) {
        photoFormData = new FormData();
        photoFormData.append('profile_picture', photoInput.files[0]);
        photoFormData.append('admin_id', adminId);
      }

      // Handle password change (only for super users)
      const currentUser = SR.currentUser;
      const canChangePassword = currentUser && currentUser.role === 'super_user';
      const newPassword = document.getElementById('edit-new-password')?.value?.trim();
      const confirmPassword = document.getElementById('edit-confirm-password')?.value?.trim();
      const isChangingPassword = canChangePassword && newPassword && newPassword.trim();

      if (isChangingPassword) {
        if (newPassword !== confirmPassword) {
          this.showError('New passwords do not match');
          return;
        }

        // Validate password strength using design system
        const passwordValidation = SRDesignSystem.password.validate(newPassword);
        if (!passwordValidation.isValid) {
          this.showError('New password does not meet security requirements. Please check the password strength indicator.');
          return;
        }
      }

      const result = await window.SystemUsersAPI.update(adminId, data);

      if (result.success) {
        // Upload photo if provided
        if (photoFormData) {
          try {
            const photoResult = await window.SystemUsersAPI.uploadPhoto(adminId, photoFormData);
            if (!photoResult.success) {
              console.warn('Photo upload failed:', photoResult.message);
              if (window.SRDesignSystem?.notifications) {
                window.SRDesignSystem.notifications.show('Administrator updated but photo upload failed', 'warning');
              }
            }
          } catch (photoError) {
            console.warn('Photo upload error:', photoError);
            if (window.SRDesignSystem?.notifications) {
              window.SRDesignSystem.notifications.show('Administrator updated but photo upload failed', 'warning');
            }
          }
        }

        // Handle password change separately if needed
        if (isChangingPassword) {
          const passwordResult = await window.SystemUsersAPI.resetPassword(adminId, {
            new_password: newPassword
          });

          if (!passwordResult.success) {
            this.showError(passwordResult.message || 'Failed to update password');
            return;
          }
        }

        this.showSuccess('Administrator updated successfully!');
        document.getElementById('edit-admin-modal').remove();
        await this.loadSystemUsersData();
        this.populateAdminsTable();
      } else {
        this.showError(result.message || 'Failed to update administrator');
      }
    } catch (error) {
      console.error('Error saving admin changes:', error);
      this.showError('Failed to update administrator');
    }
  }
};

// Export for global access
window.RegisterSystemUserComponent = RegisterSystemUserComponent;
window.ManageSystemUserComponent = ManageSystemUserComponent;

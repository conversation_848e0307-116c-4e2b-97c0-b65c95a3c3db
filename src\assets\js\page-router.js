// SmartReport Page Router
// Handles navigation between different pages/components

const PageRouter = {
  // Router state
  state: {
    currentPage: null,
    previousPage: null,
    pageHistory: [],
    loadedComponents: new Map(),
    initialized: false,
    loading: false
  },

  // Initialize router
  init() {
    // Prevent multiple initializations
    if (this.state.initialized) {
      console.warn('PageRouter already initialized');
      return;
    }

    this.setupEventListeners();
    this.state.initialized = true;
    this.loadPage('dashboard'); // Default page
  },

  // Setup event listeners
  setupEventListeners() {
    // Handle browser back/forward
    window.addEventListener('popstate', (e) => {
      if (e.state && e.state.page) {
        this.loadPage(e.state.page, false);
      }
    });

    // Handle hash changes
    window.addEventListener('hashchange', () => {
      const hash = window.location.hash.substring(1);
      if (hash && hash !== this.state.currentPage) {
        this.loadPage(hash);
      }
    });
  },

  // Load a page with enhanced UX and academic context
  async loadPage(pageId, addToHistory = true) {
    try {
      // Prevent loading the same page multiple times or loading while already loading
      if (this.state.loading) {
        console.warn(`Page loading already in progress, ignoring request for ${pageId}`);
        return false;
      }

      if (this.state.currentPage === pageId && !addToHistory) {
        console.log(`Page ${pageId} already loaded, skipping reload`);
        return true;
      }

      // Set loading state
      this.state.loading = true;
      console.log(`🔄 Loading page: ${pageId}`);

      // Check academic context for components that require it
      const hasAcademicContext = window.AcademicContext &&
                                window.AcademicContext.getActiveAcademicYear() &&
                                window.AcademicContext.getActiveTerm();

      if (!hasAcademicContext) {
        // Pages that don't require academic context (super user only pages and system pages)
        const noAcademicContextRequired = [
          'dashboard', 'academic-year-setup', 'academic-years-management',
          'register-admin', 'manage-admins', 'school-settings', 'data-import-export'
        ];

        // Pages that require academic context (all others except those explicitly exempted)
        const academicContextPages = [
          'register-teacher', 'manage-teachers', 'student-profiles',
          'register-o-level-student', 'register-a-level-student',
          'manage-streams', 'ca-configuration', 'enter-ca-scores', 'enter-exam-marks',
          'exam-types-management', 'grade-boundaries', 'o-level-report-cards'
        ];

        // Only show academic context warning for pages that explicitly require it
        // and are not in the exempted list
        if (academicContextPages.includes(pageId) && !noAcademicContextRequired.includes(pageId)) {
          console.log(`ℹ️ Page ${pageId} requires academic context but none is available - will show setup prompt`);
        }
      }

      // Cleanup previous component if it exists
      await this.cleanupPreviousComponent();

      // Update navigation state
      if (window.ModernNavigation) {
        window.ModernNavigation.setActivePage(pageId);
      }

      // Get content container
      const contentArea = document.getElementById('content-area');
      if (!contentArea) {
        console.error('Content area not found');
        return;
      }

      // Load page content directly
      this.showLoadingState(contentArea);
      const content = await this.getPageContent(pageId);

      // Update content with smooth transition and wait for DOM to be ready
      contentArea.style.opacity = '0';

      // Use Promise to ensure DOM is updated before initialization
      await new Promise((resolve) => {
        setTimeout(() => {
          contentArea.innerHTML = content;
          contentArea.style.opacity = '1';
          contentArea.style.transition = 'opacity 0.3s ease-in-out';

          // Wait for next tick to ensure DOM is fully rendered
          requestAnimationFrame(() => {
            setTimeout(resolve, 50); // Additional small delay for DOM stability
          });
        }, 150);
      });

      // Initialize page-specific functionality after DOM is ready
      await this.initializePage(pageId);

      // Update state
      this.state.previousPage = this.state.currentPage;
      this.state.currentPage = pageId;

      // Add to history
      if (addToHistory) {
        this.state.pageHistory.push(pageId);
        window.history.pushState({ page: pageId }, '', `#${pageId}`);
      }

      // Dispatch page loaded event
      document.dispatchEvent(new CustomEvent('pageLoaded', {
        detail: { pageId, previousPage: this.state.previousPage }
      }));

    } catch (error) {
      console.error(`❌ Failed to load page ${pageId}:`, error);
      this.showErrorState(document.getElementById('content-area'), error.message);
    } finally {
      // Clear loading state
      this.state.loading = false;
    }
  },

  // Update document title (browser tab only - header title removed for cleaner UI)
  updatePageTitle(pageId) {
    this.state.currentPage = pageId;
    const title = this.getPageTitle(pageId);

    // Update document title (browser tab)
    document.title = `${title} - SmartReport`;

    console.log(`📄 Document title updated: ${title}`);
  },

  // Get page title (centralized method for entire app)
  getPageTitle(pageId) {
    const titles = {
      'dashboard': 'Dashboard',
      'manage-classes-streams': 'Classes & Streams',
      'manage-streams': 'Manage Streams',
      'class-enrollments': 'Class Enrollments',
      'enroll-students': 'Enroll Students',
      'register-student': 'Register Student',
      'manage-students': 'Student Management',
      'student-profiles': 'Student Profiles',
      'enrollment-management': 'Enrollment Management',
      'subject-changes': 'Subject Changes',
      'register-teacher': 'Register Teacher',
      'manage-teachers': 'Manage Teachers',
      'o-level-subjects': 'O-Level Subjects',
      'a-level-subjects': 'A-Level Subjects',

      'o-level-grade-boundaries': 'O-Level Grade Boundaries',
      'a-level-grade-boundaries': 'A-Level Grade Boundaries',
      'ca-configuration': 'CA Configuration',
      'exam-types-management': 'Configure Exams Types',
      'enter-ca-scores': 'Enter CA Scores',
      'enter-exam-marks': 'Enter Exam Marks',
      'generate-o-level-report-cards': 'Generate O-Level Report Cards',
      'generate-a-level-report-cards': 'Generate A-Level Report Cards',
      'register-admin': 'Register Admin',
      'manage-admins': 'Manage Admins',
      'academic-year-setup': 'Academic Year Setup',
      'school-settings': 'School Settings',
      'backup-restore': 'Backup & Restore',
      'data-import-export': 'Data Import/Export'
    };
    return titles[pageId] || 'SmartReport';
  },

  // Get page content
  async getPageContent(pageId) {
    // Check if component is already loaded
    if (this.state.loadedComponents.has(pageId)) {
      const component = this.state.loadedComponents.get(pageId);
      return component.render ? component.render() : component;
    }

    // Load page content based on pageId
    switch (pageId) {
      case 'dashboard':
        // Dashboard renders its own content during initialization
        return '';

      // Academic Years Management
      case 'academic-year-setup':
        return await this.loadAcademicYearSetupComponent();

      case 'academic-years-management':
        return await this.loadAcademicYearsManagementComponent();

      // Classes & Streams Management
      case 'manage-classes-streams':
        return await this.loadComponent('classes-streams', 'ClassesStreamsComponent');

      case 'manage-streams':
        return await this.loadComponent('classes-streams', 'ManageStreamsComponent');

      // Grade Boundaries
      case 'o-level-grade-boundaries':
        return await this.loadComponent('grade-boundaries', 'OLevelGradeBoundariesComponent');

      case 'a-level-grade-boundaries':
        return await this.loadComponent('grade-boundaries', 'ALevelGradeBoundariesComponent');

      case 'register-student':
        return await this.loadComponent('student-management', 'RegisterStudentComponent');

      case 'register-o-level-student':
        return await this.loadComponent('register-o-level-student', 'RegisterOLevelStudentComponent');

      case 'register-a-level-student':
        return await this.loadComponent('register-a-level-student', 'RegisterALevelStudentComponent');

      case 'manage-students':
        return await this.loadComponent('student-management', 'StudentManagementComponents');

      case 'student-profiles':
        console.log('🔄 Loading student-profiles page with ManageStudentsComponent');
        const result = await this.loadComponent('student-management', 'ManageStudentsComponent');
        console.log('✅ ManageStudentsComponent loaded, content length:', result.length);
        return result;

      case 'enrollment-management':
        return await this.loadComponent('student-management', 'StudentEnrollmentComponent');

      case 'subject-change-requests':
        return await this.loadComponent('student-management', 'SubjectChangeRequestsComponent');

      case 'register-teacher':
        return await this.loadComponent('teacher-management', 'RegisterTeacherComponent');

      case 'manage-teachers':
        return await this.loadComponent('teacher-management', 'ManageTeachersComponent');

      // School Settings (Super User Only)
      case 'school-settings':
        return await this.loadSuperUserComponent('school-settings', 'SchoolSettingsManagementComponent');

      // Admin Management (Super User Only)
      case 'register-admin':
        return await this.loadSuperUserComponent('user-management', 'RegisterSystemUserComponent');

      case 'manage-admins':
        return await this.loadSuperUserComponent('user-management', 'ManageSystemUserComponent');

      // Report Cards
      case 'generate-o-level-report-cards':
        return await this.loadComponent('o-level-report-cards', 'OLevelReportCardsComponent');

      case 'generate-a-level-report-cards':
        return this.getComponentPlaceholder(this.getPageTitle(pageId));

      // Assessment Management 
      case 'exam-types-management':
        return await this.loadComponent('exam-types-management', 'ExamTypesManagementComponent');

      case 'ca-configuration':
        return await this.loadComponent('ca-configuration', 'CAConfigurationComponent');

      case 'enter-ca-scores':
        return await this.loadComponent('assessment-management', 'EnterCAScoresComponent');

      case 'enter-exam-marks':
        return await this.loadComponent('assessment-management', 'EnterExamMarksComponent');

      // System Settings (Super User Only)
      case 'data-import-export':
        return await this.loadSuperUserComponent('import-export-data', 'ImportExportDataManagementComponent');

      default:
        return this.getNotFoundPage();
    }
  },

  // Load Academic Year Setup component (special handling)
  async loadAcademicYearSetupComponent() {
    try {
      // Check if the component class is available
      if (window.AcademicYearSetupComponent) {
        console.log('🔧 Creating Academic Year Setup component instance...');

        // First check if setup is actually needed
        const setupNeeded = await this.checkIfAcademicSetupNeeded();

        if (!setupNeeded) {
          // Setup already exists, show info message
          return this.getAcademicYearAlreadyConfiguredMessage();
        }

        // Create a new instance of the component
        const component = new window.AcademicYearSetupComponent();

        // Render the component
        const content = component.render();

        // Validate rendered content
        const validatedContent = this.validateComponentContent(content, 'AcademicYearSetupComponent');

        // Store the component instance for later initialization
        this.state.loadedComponents.set('AcademicYearSetupComponent', {
          content: validatedContent,
          component
        });

        // Register the specific instance with the lifecycle manager
        if (window.ComponentLifecycleManager) {
          window.ComponentLifecycleManager.registerComponent('AcademicYearSetupComponent', component, {
            requiredElements: [],
            dependencies: []
          });
        }

        return validatedContent;
      } else {
        console.error('❌ AcademicYearSetupComponent class not found');
        return this.getComponentPlaceholder('AcademicYearSetupComponent');
      }
    } catch (error) {
      console.error('❌ Failed to load Academic Year Setup component:', error);
      return this.getErrorPlaceholder('AcademicYearSetupComponent', error.message);
    }
  },

  // Load Academic Years Management component
  async loadAcademicYearsManagementComponent() {
    try {
      // Check if the component class is available
      if (window.AcademicYearManagementComponent) {
        console.log('🔧 Creating Academic Year Management component instance...');

        // Render the component
        const content = window.AcademicYearManagementComponent.render();

        // Validate rendered content
        const validatedContent = this.validateComponentContent(content, 'AcademicYearManagementComponent');

        // Store the component instance for later initialization
        this.state.loadedComponents.set('AcademicYearManagementComponent', {
          content: validatedContent,
          component: window.AcademicYearManagementComponent
        });

        return validatedContent;
      } else {
        console.error('❌ AcademicYearManagementComponent class not found');
        return this.getComponentPlaceholder('AcademicYearManagementComponent');
      }
    } catch (error) {
      console.error('❌ Failed to load Academic Year Management component:', error);
      return this.getErrorPlaceholder('AcademicYearManagementComponent', error.message);
    }
  },

  // Check if academic setup is needed
  async checkIfAcademicSetupNeeded() {
    try {
      // Check if academic years exist
      const yearsResponse = await window.AcademicYearsAPI.getAll();
      const termsResponse = await window.TermsAPI.getAll();

      // If both academic years and terms exist, setup is not needed
      return !(yearsResponse.success && yearsResponse.data.length > 0 &&
               termsResponse.success && termsResponse.data.length > 0);
    } catch (error) {
      console.error('Error checking setup status:', error);
      // If there's an error, assume setup is needed
      return true;
    }
  },

  // Get "already configured" message
  getAcademicYearAlreadyConfiguredMessage() {
    return `
      <div class="space-y-6">
        <div class="flex flex-col items-center justify-center min-h-96 text-center">
          <div class="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mb-6">
            <i class="fas fa-check-circle text-green-600 text-4xl"></i>
          </div>
          <div class="max-w-md">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Academic Year Already Configured</h2>
            <p class="text-gray-600 mb-8">
              Your academic year and terms have already been set up.
            </p>
            <div class="flex justify-center">
              <button onclick="window.PageRouter.loadPage('academic-years-management')" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-calendar-check mr-2"></i>
                Manage
              </button>
            </div>
          </div>
        </div>
      </div>
    `;
  },

  // Load component dynamically
  async loadComponent(_module, componentName) {
    try {
      // Check if component is already loaded globally
      if (window[componentName]) {
        const component = window[componentName];

        // First render the component
        let content = '';
        if (component.render && typeof component.render === 'function') {
          content = component.render();
        } else if (typeof component === 'string') {
          content = component;
        } else {
          content = this.getComponentPlaceholder(componentName);
        }

        // Validate rendered content
        content = this.validateComponentContent(content, componentName);

        // Store the content for later initialization
        this.state.loadedComponents.set(componentName, { content, component });

        return content;
      }

      // Return placeholder if component not found
      return this.getComponentPlaceholder(componentName);

    } catch (error) {
      console.error(`Failed to load component ${componentName}:`, error);
      return this.getErrorPlaceholder(componentName, error.message);
    }
  },

  // Load component with super user access control
  async loadSuperUserComponent(_module, componentName) {
    try {
      // Check if user is super user
      const currentUser = SR.currentUser;
      const isSuperUser = currentUser && currentUser.role === 'super_user';

      if (!isSuperUser) {
        return `
          <div class="flex items-center justify-center min-h-96">
            <div class="text-center">
              <div class="text-gray-400 mb-4">
                <i class="fas fa-user-shield text-6xl"></i>
              </div>
              <h2 class="text-2xl font-bold text-gray-900 mb-2">Access Restricted</h2>
              <p class="text-gray-600 mb-4">This section is only available to Super Users.</p>
              <p class="text-sm text-gray-500">Contact your system administrator if you need access to user management features.</p>
            </div>
          </div>
        `;
      }

      // Load component normally if user is super user (components are loaded statically but registered conditionally)
      return await this.loadComponent(_module, componentName);

    } catch (error) {
      console.error(`Failed to load super user component ${componentName}:`, error);
      return this.getErrorPlaceholder(componentName, error.message);
    }
  },

  // Initialize page-specific functionality
  async initializePage(pageId) {
    // Initialize component if it was loaded
    await this.initializeLoadedComponent(pageId);
    // Initialize common page functionality
    this.initializeCommonFeatures();
  },

  // Cleanup previous component using lifecycle manager
  async cleanupPreviousComponent() {
    try {
      if (!this.state.currentPage) return;

      // Use ComponentLifecycleManager for cleanup
      if (window.ComponentLifecycleManager) {
        console.log(`🧹 Cleaning up components for page: ${this.state.currentPage}`);
        await window.ComponentLifecycleManager.cleanupPageComponents(this.state.currentPage);
      } else {
        console.warn('ComponentLifecycleManager not available - skipping cleanup');
      }
    } catch (error) {
      console.error('Error during component cleanup:', error);
    }
  },



  // Initialize loaded component using lifecycle manager
  async initializeLoadedComponent(pageId) {
    try {
      // Map page IDs to their corresponding component names
      const pageComponentMap = {
        'dashboard': 'Dashboard',
        'academic-year-setup': 'AcademicYearSetupComponent',
        'academic-years-management': 'AcademicYearManagementComponent',
        'register-admin': 'RegisterSystemUserComponent',
        'manage-admins': 'ManageSystemUserComponent',
        'manage-streams': 'ManageStreamsComponent',
        'manage-teachers': 'ManageTeachersComponent',
        'manage-students': 'StudentManagementComponents',
        'student-profiles': 'ManageStudentsComponent',
        'enrollment-management': 'StudentEnrollmentComponent',
        'register-teacher': 'RegisterTeacherComponent',
        'register-student': 'RegisterStudentComponent',
        'register-o-level-student': 'RegisterOLevelStudentComponent',
        'register-a-level-student': 'RegisterALevelStudentComponent',
        'ca-configuration': 'CAConfigurationComponent',
        'exam-types-management': 'ExamTypesManagementComponent',
        'enter-ca-scores': 'EnterCAScoresComponent',
        'enter-exam-marks': 'EnterExamMarksComponent',
        'o-level-grade-boundaries': 'OLevelGradeBoundariesComponent',
        'a-level-grade-boundaries': 'ALevelGradeBoundariesComponent',
        'school-settings': 'SchoolSettingsManagementComponent',
        'data-import-export': 'ImportExportDataManagementComponent',
        'generate-o-level-report-cards': 'OLevelReportCardsComponent'
      };

      const expectedComponentName = pageComponentMap[pageId];

      if (expectedComponentName) {
        // Use ComponentLifecycleManager for initialization
        if (window.ComponentLifecycleManager) {
          console.log(`🔧 Initializing component using lifecycle manager: ${expectedComponentName}`);
          const success = await window.ComponentLifecycleManager.initializeComponent(expectedComponentName, pageId);

          if (success) {
            console.log(`✅ Component ${expectedComponentName} initialized successfully via lifecycle manager`);
          } else {
            console.warn(`⚠️ Component ${expectedComponentName} initialization failed`);
          }
        } else {
          console.warn('ComponentLifecycleManager not available - component initialization skipped');
        }
      } else {
        console.log(`ℹ️ No component mapping found for page: ${pageId}`);
      }
    } catch (error) {
      console.error(`Failed to initialize component for page ${pageId}:`, error);
    }
  },

  // Validate component content
  validateComponentContent(content, componentName) {
    // Check if content is valid
    if (!content || typeof content !== 'string') {
      console.warn(`Component ${componentName} returned invalid content, using placeholder`);
      return this.getComponentPlaceholder(componentName);
    }

    // Check if content is empty or just whitespace
    if (content.trim().length === 0) {
      console.warn(`Component ${componentName} returned empty content, using placeholder`);
      return this.getComponentPlaceholder(componentName);
    }

    // Check if content looks like HTML
    if (!content.includes('<') || !content.includes('>')) {
      console.warn(`Component ${componentName} returned non-HTML content: ${content.substring(0, 100)}...`);
      return `
        <div class="space-y-6">
          <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">${componentName}</h2>
            <div class="p-4 bg-gray-50 rounded-lg">
              <pre class="text-sm text-gray-700">${content}</pre>
            </div>
          </div>
        </div>
      `;
    }

    return content;
  },

  // Initialize common page features
  initializeCommonFeatures() {
    // Initialize tooltips, modals, etc.
    this.initializeTooltips();
    this.initializeModals();
  },

  // Initialize tooltips
  initializeTooltips() {
    // Add tooltip functionality if needed
  },

  // Initialize modals
  initializeModals() {
    // Add modal functionality if needed
  },

  // Show loading state - centralized for all components
  showLoadingState(container, message = 'Loading...') {
    if (window.SRDesignSystem && window.SRDesignSystem.layouts && window.SRDesignSystem.layouts.loadingState) {
      // Use the design system's loading state, centered in the content area
      container.innerHTML = `
        <div class="flex items-center justify-center min-h-[400px] w-full">
          ${window.SRDesignSystem.layouts.loadingState(message)}
        </div>
      `;
    } else {
      // Fallback if design system is not available
      container.innerHTML = `
        <div class="flex items-center justify-center min-h-[400px] w-full">
          <div class="text-center">
            <div class="w-16 h-16 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin mx-auto mb-4"></div>
            <p class="text-gray-600 font-medium">${message}</p>
          </div>
        </div>
      `;
    }
  },

  // Show error state
  showErrorState(container, message) {
    container.innerHTML = `
      <div class="flex items-center justify-center h-64">
        <div class="text-center">
          <i class="fas fa-exclamation-triangle text-red-500 text-4xl mb-4"></i>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Error Loading Page</h3>
          <p class="text-gray-500 mb-4">${message}</p>
          <button onclick="location.reload()" class="btn btn-primary">
            <i class="fas fa-refresh mr-2"></i>
            Reload Page
          </button>
        </div>
      </div>
    `;
  },



  // Get component placeholder
  getComponentPlaceholder(componentName) {
    return `
      <div class="space-y-6">
        <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h2 class="text-2xl font-bold text-gray-900 mb-4">${componentName}</h2>
          <p class="text-gray-600">This component is being developed...</p>
          <div class="mt-4 p-4 bg-blue-50 rounded-lg">
            <p class="text-blue-800 text-sm">
              <i class="fas fa-info-circle mr-2"></i>
              This feature will be available soon. The component structure is ready for implementation.
            </p>
          </div>
        </div>
      </div>
    `;
  },

  // Get error placeholder
  getErrorPlaceholder(componentName, error) {
    return `
      <div class="space-y-6">
        <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h2 class="text-2xl font-bold text-gray-900 mb-4">Error Loading ${componentName}</h2>
          <div class="p-4 bg-red-50 rounded-lg">
            <p class="text-red-800 text-sm">
              <i class="fas fa-exclamation-triangle mr-2"></i>
              ${error}
            </p>
          </div>
        </div>
      </div>
    `;
  },

  // Get not found page
  getNotFoundPage() {
    return `
      <div class="flex items-center justify-center h-64">
        <div class="text-center">
          <i class="fas fa-search text-gray-400 text-6xl mb-4"></i>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">Page Not Found</h3>
          <p class="text-gray-500 mb-4">The requested page could not be found.</p>
          <button onclick="window.PageRouter.loadPage('dashboard')" class="btn btn-primary">
            <i class="fas fa-home mr-2"></i>
            Go to Dashboard
          </button>
        </div>
      </div>
    `;
  },

  // Navigate back
  goBack() {
    if (this.state.pageHistory.length > 1) {
      this.state.pageHistory.pop(); // Remove current page
      const previousPage = this.state.pageHistory[this.state.pageHistory.length - 1];
      this.loadPage(previousPage, false);
    }
  },

  // Get current page
  getCurrentPage() {
    return this.state.currentPage;
  }
};

// Export to global scope
window.PageRouter = PageRouter;

/**
 * Validation Middleware
 * Provides middleware functions for automatic validation of requests
 */

const { BusinessValidation } = require('../utils/validation');
const { executeQuery } = require('../../database/connection');

/**
 * Middleware to ensure academic context is properly set before allowing data creation
 */
const requireAcademicContext = () => {
  return async (req, res, next) => {
    try {
      console.log('🔍 Checking academic context before data creation...');

      // Check for active academic year
      const yearQuery = 'SELECT id, name FROM academic_years WHERE is_active = 1 LIMIT 1';
      const yearResult = await executeQuery(yearQuery);

      if (!yearResult.success || yearResult.data.length === 0) {
        console.error('❌ No active academic year found');
        return res.status(400).json({
          success: false,
          message: 'Academic context not set. Please set up an active academic year before creating data.',
          code: 'ACADEMIC_YEAR_REQUIRED',
          setupRequired: true
        });
      }

      // Check for active term
      const termQuery = 'SELECT id, name, academic_year_id FROM terms WHERE is_active = 1 LIMIT 1';
      const termResult = await executeQuery(termQuery);

      if (!termResult.success || termResult.data.length === 0) {
        console.error('❌ No active term found');
        return res.status(400).json({
          success: false,
          message: 'Academic context not set. Please set up an active term before creating data.',
          code: 'ACTIVE_TERM_REQUIRED',
          setupRequired: true
        });
      }

      // Verify the active term belongs to the active academic year
      const activeYear = yearResult.data[0];
      const activeTerm = termResult.data[0];

      if (activeTerm.academic_year_id !== activeYear.id) {
        console.error('❌ Active term does not belong to active academic year');
        return res.status(400).json({
          success: false,
          message: 'Academic context mismatch. The active term does not belong to the active academic year.',
          code: 'ACADEMIC_CONTEXT_MISMATCH',
          setupRequired: true
        });
      }

      console.log('✅ Academic context validated:', {
        academicYear: activeYear.name,
        term: activeTerm.name
      });

      // Add academic context to request for use in routes
      req.academicContext = {
        academicYear: activeYear,
        term: activeTerm
      };

      next();
    } catch (error) {
      console.error('Academic context validation error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to validate academic context'
      });
    }
  };
};

/**
 * Middleware to validate teacher data
 */
const validateTeacher = (isUpdate = false) => {
  return async (req, res, next) => {
    try {
      const teacherData = { ...req.body };

      const validationErrors = await BusinessValidation.validateTeacher(teacherData, isUpdate, req.params.id);

      if (validationErrors.length > 0) {
        return res.status(400).json(BusinessValidation.formatValidationErrors(validationErrors));
      }

      // Add validated data to request for use in route handler
      req.validatedData = teacherData;
      next();
    } catch (error) {
      console.error('Teacher validation middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Validation error occurred'
      });
    }
  };
};

/**
 * Middleware to validate student data
 */
const validateStudent = (isUpdate = false) => {
  return async (req, res, next) => {
    try {
      const studentData = req.body;

      const validationErrors = await BusinessValidation.validateStudent(studentData, isUpdate, req.params.id);

      if (validationErrors.length > 0) {
        return res.status(400).json(BusinessValidation.formatValidationErrors(validationErrors));
      }

      req.validatedData = studentData;
      next();
    } catch (error) {
      console.error('Student validation middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Validation error occurred'
      });
    }
  };
};

/**
 * Middleware to validate academic year data
 */
const validateAcademicYear = (isUpdate = false) => {
  return async (req, res, next) => {
    try {
      const academicYearData = req.body;

      const validationErrors = await BusinessValidation.validateAcademicYear(academicYearData, isUpdate, req.params.id);

      if (validationErrors.length > 0) {
        return res.status(400).json(BusinessValidation.formatValidationErrors(validationErrors));
      }

      req.validatedData = academicYearData;
      next();
    } catch (error) {
      console.error('Academic year validation middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Validation error occurred'
      });
    }
  };
};

/**
 * Middleware to validate term data
 */
const validateTerm = (isUpdate = false) => {
  return async (req, res, next) => {
    try {
      const termData = req.body;

      const validationErrors = await BusinessValidation.validateTerm(termData, isUpdate, req.params.id);

      if (validationErrors.length > 0) {
        return res.status(400).json(BusinessValidation.formatValidationErrors(validationErrors));
      }

      req.validatedData = termData;
      next();
    } catch (error) {
      console.error('Term validation middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Validation error occurred'
      });
    }
  };
};

/**
 * Middleware to validate competency scores
 */
const validateCompetencyScore = () => {
  return (req, res, next) => {
    try {
      const { competency_score } = req.body;

      if (competency_score !== undefined) {
        const errors = BusinessValidation.validateCompetencyScore(competency_score);
        if (errors.length > 0) {
          return res.status(400).json(BusinessValidation.formatValidationErrors(errors));
        }
      }

      next();
    } catch (error) {
      console.error('Competency score validation middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Validation error occurred'
      });
    }
  };
};

/**
 * Middleware to validate exam marks and percentages
 */
const validateExamMarks = () => {
  return (req, res, next) => {
    try {
      const errors = BusinessValidation.validateExamMarks(req.body);
      if (errors.length > 0) {
        return res.status(400).json(BusinessValidation.formatValidationErrors(errors));
      }

      next();
    } catch (error) {
      console.error('Exam marks validation middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Validation error occurred'
      });
    }
  };
};

/**
 * Middleware to validate subject selection
 */
const validateSubjectSelection = () => {
  return async (req, res, next) => {
    try {
      const errors = await BusinessValidation.validateSubjectSelection(req.body);
      if (errors.length > 0) {
        return res.status(400).json(BusinessValidation.formatValidationErrors(errors));
      }

      next();
    } catch (error) {
      console.error('Subject selection validation middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Validation error occurred'
      });
    }
  };
};

/**
 * Generic validation middleware that can be configured for different entity types
 */
const validateEntity = (entityType, isUpdate = false) => {
  return async (req, res, next) => {
    try {
      const entityData = req.body;

      let validationErrors = [];

      switch (entityType) {
        case 'teacher':
          validationErrors = await BusinessValidation.validateTeacher(entityData, isUpdate, req.params.id);
          break;
        case 'student':
          validationErrors = await BusinessValidation.validateStudent(entityData, isUpdate, req.params.id);
          break;
        case 'academicYear':
          validationErrors = await BusinessValidation.validateAcademicYear(entityData, isUpdate, req.params.id);
          break;
        case 'term':
          validationErrors = await BusinessValidation.validateTerm(entityData, isUpdate, req.params.id);
          break;
        default:
          throw new Error(`Unknown entity type: ${entityType}`);
      }

      if (validationErrors.length > 0) {
        return res.status(400).json(BusinessValidation.formatValidationErrors(validationErrors));
      }

      req.validatedData = entityData;
      next();
    } catch (error) {
      console.error(`${entityType} validation middleware error:`, error);
      res.status(500).json({
        success: false,
        message: 'Validation error occurred'
      });
    }
  };
};

/**
 * Middleware to require super user role for admin management operations
 */
const requireSuperUser = () => {
  return async (req, res, next) => {
    try {
      // Check if user is authenticated and has super_user role
      if (!req.user || req.user.role !== 'super_user') {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Super user privileges required.',
          code: 'INSUFFICIENT_PRIVILEGES'
        });
      }

      next();
    } catch (error) {
      console.error('Super user validation error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to validate user privileges'
      });
    }
  };
};

/**
 * Middleware to prevent system_admin from modifying super_user accounts
 */
const preventSuperUserModification = () => {
  return async (req, res, next) => {
    try {
      const { id } = req.params;

      // If current user is super_user, allow all operations
      if (req.user && req.user.role === 'super_user') {
        return next();
      }

      // Check if target user is super_user
      const { executeQuery } = require('../../database/connection');
      const userQuery = 'SELECT role FROM system_users WHERE id = ?';
      const userResult = await executeQuery(userQuery, [id]);

      if (!userResult.success) {
        throw new Error(userResult.error);
      }

      if (userResult.data.length > 0 && userResult.data[0].role === 'super_user') {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Cannot modify super user accounts.',
          code: 'CANNOT_MODIFY_SUPER_USER'
        });
      }

      next();
    } catch (error) {
      console.error('Super user modification prevention error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to validate operation permissions'
      });
    }
  };
};

/**
 * Middleware to prevent deletion of super_user accounts by system_admin
 * Also prevents deletion of the last super_user account
 */
const preventSuperUserDeletion = () => {
  return async (req, res, next) => {
    try {
      const { id } = req.params;
      const { executeQuery } = require('../../database/connection');

      // Check if target user is super_user
      const userQuery = 'SELECT role FROM system_users WHERE id = ?';
      const userResult = await executeQuery(userQuery, [id]);

      if (!userResult.success) {
        throw new Error(userResult.error);
      }

      if (userResult.data.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'System user not found'
        });
      }

      const targetUser = userResult.data[0];

      // If target is super_user, only super_user can delete
      if (targetUser.role === 'super_user') {
        if (!req.user || req.user.role !== 'super_user') {
          return res.status(403).json({
            success: false,
            message: 'Access denied. Only super users can delete super user accounts.',
            code: 'CANNOT_DELETE_SUPER_USER'
          });
        }

        // Prevent deletion of the last super_user
        const superUserCountQuery = 'SELECT COUNT(*) as count FROM system_users WHERE role = "super_user" AND is_active = TRUE';
        const superUserCountResult = await executeQuery(superUserCountQuery);

        if (superUserCountResult.success && superUserCountResult.data[0].count <= 1) {
          return res.status(400).json({
            success: false,
            message: 'Cannot delete the last super user account',
            code: 'LAST_SUPER_USER'
          });
        }
      }

      next();
    } catch (error) {
      console.error('Super user deletion prevention error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to validate deletion permissions'
      });
    }
  };
};

module.exports = {
  requireAcademicContext,
  validateTeacher,
  validateStudent,
  validateAcademicYear,
  validateTerm,
  validateCompetencyScore,
  validateExamMarks,
  validateSubjectSelection,
  validateEntity,
  requireSuperUser,
  preventSuperUserModification,
  preventSuperUserDeletion
};

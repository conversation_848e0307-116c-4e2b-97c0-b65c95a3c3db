// SR - Main Application JavaScript
// Academic Information Management System

// Global application state for Electron Desktop Application
window.SR = {
  currentUser: null,
  currentAcademicYear: null,
  currentTerm: null,
  get serverUrl() {
    return window.SRConfig.getServerUrl();
  },
  isInitialized: false,
  isElectron: true, // Flag to indicate this is an Electron app
  config: {
    // Environment configuration for Electron - managed by SRConfig
    get API_BASE_URL() { return window.SRConfig.getApiUrl(); },
    get SERVER_URL() { return window.SRConfig.getServerUrl(); },
    get DEBUG_MODE() { return window.SRConfig.get('development.debugMode', true); },
    get NODE_ENV() { return window.SRConfig.getEnvVar('NODE_ENV', 'development'); }
  }
};

// Application initialization
document.addEventListener('DOMContentLoaded', async function() {
  // Initialize modern components first
  initializeModernComponents();

  // Verify all UI/UX components are loaded
  verifyUIComponents();



  try {
    // Show loading screen
    showLoadingScreen('Initializing system...');

    // Check server connection (non-blocking)
    const serverConnected = await checkServerConnection();
    if (serverConnected) {
      // Check database status via API if needed
      await initializeDatabaseViaAPI();
    }

    // Check authentication
    console.log('🔐 Checking authentication...');
    await checkAuthentication();
    console.log('✅ Authentication check complete');

    // Initialize application
    console.log('🚀 Initializing application...');
    await initializeApplication();

    console.log('✅ SR Application Started Successfully');

  } catch (error) {

    // Always show login screen if initialization fails
    showLoginScreen();

    // Show error notification if available
    if (window.showNotification) {
      showNotification('System initialization failed. Please try logging in.', 'warning');
    }
  }
});

// Verify all UI components are loaded
function verifyUIComponents() {
  const components = [
    { name: 'Layout', obj: window.Layout },
    { name: 'PageRouter', obj: window.PageRouter },
    { name: 'Dashboard', obj: window.Dashboard }
  ];

  let allLoaded = true;
  components.forEach(component => {
    if (!component.obj) {
      allLoaded = false;
    }
  });
}

// Check server connection
async function checkServerConnection() {
  try {
    updateLoadingText('Connecting to server...');

    // Add timeout to prevent hanging
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    const response = await fetch(`${window.SRConfig.getServerUrl()}/health`, {
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error('Server is not responding');
    }

    const health = await response.json();
    console.log('📡 Server connection established:', health);

    // Handle database initialization status
    if (health.status === 'INITIALIZING') {
      updateLoadingText('Setting up database for first time...');
      console.log('📊 Database is being initialized...');

      // Wait a bit and try again (with retry limit)
      await new Promise(resolve => setTimeout(resolve, 3000));
      return await checkServerConnection(); // Retry
    }

    if (!health.database.connected) {
      throw new Error('Database connection failed');
    }

    // Update current academic info
    if (health.database.stats) {
      SR.currentAcademicYear = health.database.stats.currentAcademicYear;
      SR.currentTerm = health.database.stats.currentTerm;

      // Update navbar immediately if elements exist
      updateAcademicInfoDisplay();
    }

  } catch (error) {
    console.warn('⚠️ Server connection failed, continuing in offline mode:', error);
    return false;
  }
}

// Initialize database via API (for frontend)
async function initializeDatabaseViaAPI() {
  try {
    updateLoadingText('Checking database status...');

    const response = await fetch(`${window.SRConfig.getServerUrl()}/api/init-database`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      // If API is not available, database might already be initialized
      console.log('ℹ️ Database API not available, assuming database is ready');
      return;
    }

    const result = await response.json();

    if (!result.success) {
      console.warn('⚠️ Database initialization API returned error:', result.error);
      // Don't throw - database might already be initialized
      return;
    }

    console.log('📊 Database status checked:', result.message);

  } catch (error) {
    console.warn('⚠️ Database initialization check failed:', error.message);
    // Don't throw error for database initialization - it might already be initialized
    // Just log the error and continue
    console.log('ℹ️ Continuing despite database check error - database may already be ready');
  }
}

// Check authentication
async function checkAuthentication() {
  try {
    updateLoadingText('Checking authentication...');

    const token = localStorage.getItem('smartreport_token');
    if (!token) {
      console.log('ℹ️ No authentication token found - showing login screen');
      showLoginScreen();
      return;
    }

    // Validate token with server
    const response = await fetch(`${window.SRConfig.getApiUrl('/auth/validate')}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const userData = await response.json();
      if (userData.success && userData.user) {
        SR.currentUser = userData.user;
        console.log('✅ User authenticated:', userData.user.username);
        console.log('👤 User data loaded:', userData.user);

        // Enable application menu for authenticated user
        if (window.require) {
          const { ipcRenderer } = window.require('electron');
          await ipcRenderer.invoke('login-success');
        }

        await showMainApplication();

        // Update user interface with the loaded user data
        await updateUserInterface();

        // Update user profile image in top bar
        if (window.Layout && window.Layout.updateUserProfileImage) {
          window.Layout.updateUserProfileImage();
        }

        // Note: Academic year check is handled in auth.js after login
      } else {
        console.log('ℹ️ Invalid user data received');
        localStorage.removeItem('smartreport_token');
        showLoginScreen();
      }
    } else {
      console.log('ℹ️ Token validation failed - showing login screen');
      localStorage.removeItem('smartreport_token');
      showLoginScreen();
    }

  } catch (error) {
    console.error('❌ Authentication check failed:', error);
    console.log('🔄 Showing login screen due to authentication error');
    localStorage.removeItem('smartreport_token');
    showLoginScreen();
  }
}

// Initialize main application
async function initializeApplication() {
  if (!SR.currentUser) {
    return; // Will show login screen
  }

  try {
    updateLoadingText('Loading application...');

    // Update UI with current user info
    await updateUserInterface();

    // Initialize authentication components
    if (window.AuthManager) {
      AuthManager.updateUserInterface();
    }


    // Mark as initialized
    SR.isInitialized = true;

    // Hide loading screen
    hideLoadingScreen();

  } catch (error) {
    console.error('❌ Application initialization failed:', error);
    throw error;
  }
}

// Show loading screen
function showLoadingScreen(message = 'Loading...') {
  const loadingScreen = document.getElementById('loading-screen');
  const loginScreen = document.getElementById('login-screen');
  const mainApp = document.getElementById('main-app');
  
  loadingScreen.style.display = 'flex';
  loginScreen.style.display = 'none';
  mainApp.style.display = 'none';
  
  updateLoadingText(message);
}

// Update loading text
function updateLoadingText(text) {
  const loadingMessage = document.getElementById('loading-message');
  if (loadingMessage) {
    loadingMessage.textContent = text;
  }
}

// Hide loading screen
function hideLoadingScreen() {
  const loadingScreen = document.getElementById('loading-screen');
  if (loadingScreen) {
    loadingScreen.style.display = 'none';
    console.log('✅ Loading screen hidden');
  } else {
    console.warn('⚠️ Loading screen element not found');
  }
}

// Show login screen
function showLoginScreen() {
  console.log('🔐 Showing login screen...');

  // Check if we're in the app layout (after login) or original layout (initial load)
  const appLayout = document.getElementById('app-layout');

  if (appLayout) {
    // We're in app layout - need to restore original login screen
    console.log('🔄 Restoring login screen from app layout...');
    restoreLoginScreen();
  } else {
    // We're in original layout - use original elements
    const loadingScreen = document.getElementById('loading-screen');
    const loginScreen = document.getElementById('login-screen');
    const mainApp = document.getElementById('main-app');

    if (!loadingScreen || !loginScreen || !mainApp) {
      console.error('❌ Required screen elements not found!', {
        loadingScreen: !!loadingScreen,
        loginScreen: !!loginScreen,
        mainApp: !!mainApp
      });
      return;
    }

    loadingScreen.style.display = 'none';
    loginScreen.style.display = 'flex';
    mainApp.style.display = 'none';
  }

  console.log('✅ Login screen displayed successfully');
}

// Restore login screen when logging out from app layout
function restoreLoginScreen() {
  console.log('🔄 Restoring original login screen...');

  // Get the original HTML structure from the initial page load
  // For now, we'll reload the page to get back to the login screen
  // This is the safest approach to ensure all original elements are restored
  window.location.reload();
}

// Show main application
async function showMainApplication() {
  console.log('🚀 Showing main application...');

  // Check if we're already in app layout (after logout/login cycle)
  const appLayout = document.getElementById('app-layout');

  if (appLayout) {
    console.log('✅ App layout already exists, skipping screen transition');

    // Just ensure the layout is visible and update user interface
    appLayout.style.display = 'flex';

    // Update user interface
    await updateUserInterface();

    // Initialize page router if needed
    if (window.PageRouter && !window.PageRouter.state.isInitialized) {
      console.log('🧭 Re-initializing Page Router...');
      window.PageRouter.init();
    }

    return;
  }

  // Original screen transition logic for first-time login
  const loadingScreen = document.getElementById('loading-screen');
  const loginScreen = document.getElementById('login-screen');
  const mainApp = document.getElementById('main-app');

  // Check if all required elements exist
  if (!loadingScreen || !loginScreen || !mainApp) {
    console.error('❌ Required screen elements not found for main app!', {
      loadingScreen: !!loadingScreen,
      loginScreen: !!loginScreen,
      mainApp: !!mainApp
    });
    return;
  }

  loadingScreen.style.display = 'none';
  loginScreen.style.display = 'none';
  mainApp.style.display = 'block';

  // Initialize layout system first
  if (window.Layout) {
    console.log('🎨 Initializing Layout System...');
    window.Layout.init();
  }

  // Initialize page router after layout is ready
  setTimeout(() => {
    if (window.PageRouter) {
      console.log('🧭 Initializing Page Router...');
      window.PageRouter.init();
    }
  }, 100);

  // Update user interface
  await updateUserInterface();
}

// Update user interface
async function updateUserInterface() {
  if (!SR.currentUser) {
    console.warn('⚠️ No current user data available for UI update');
    return;
  }

  console.log('🔄 Updating user interface with:', SR.currentUser);

  // Update user name in various elements
  const userNameElement = document.getElementById('user-name');
  if (userNameElement) {
    const fullName = `${SR.currentUser.first_name || ''} ${SR.currentUser.last_name || ''}`.trim();
    userNameElement.textContent = fullName || 'Unknown User';
    console.log('✅ Updated user-name element:', fullName);
  }

  // Update user display name in the dropdown
  const userDisplayName = document.getElementById('user-display-name');
  if (userDisplayName) {
    const fullName = `${SR.currentUser.first_name || ''} ${SR.currentUser.last_name || ''}`.trim();
    userDisplayName.textContent = fullName || 'Unknown User';
    console.log('✅ Updated user-display-name element:', fullName);
  }

  // Update user role display
  const userRoleElements = document.querySelectorAll('.user-role');
  userRoleElements.forEach(element => {
    const roleNames = {
      'super_user': 'Super User',
      'system_admin': 'System Administrator',
      'class_teacher': 'Class Teacher',
      'subject_teacher': 'Subject Teacher'
    };
    element.textContent = roleNames[SR.currentUser.role] || 'Unknown Role';
  });

  // Refresh super user component registrations
  if (window.ComponentLifecycleManager && window.ComponentLifecycleManager.refreshSuperUserComponents) {
    window.ComponentLifecycleManager.refreshSuperUserComponents();
  }

  // Refresh navigation to show/hide role-based menu items
  if (window.ModernNavigation && window.ModernNavigation.refresh) {
    window.ModernNavigation.refresh();
  }

  // Update academic year and term
  updateAcademicInfoDisplay();

}

// Load role-specific components (now just logs since components are loaded statically)
async function loadRoleSpecificComponents() {
  if (!SR.currentUser) {
    console.warn('⚠️ No current user data available for role-specific component loading');
    return;
  }

  const isSuperUser = SR.currentUser.role === 'super_user';
  console.log(`ℹ️ User role: ${SR.currentUser.role}, Super user components ${isSuperUser ? 'will be' : 'will not be'} available`);
}



// Update academic year and term display
function updateAcademicInfoDisplay() {
  const academicYearElement = document.getElementById('current-academic-year');
  const termElement = document.getElementById('current-term');
  const academicPeriodElement = document.getElementById('academic-period');

  // Handle case when no academic data is set up
  if (!SR.currentAcademicYear || !SR.currentTerm) {
    if (academicYearElement) {
      academicYearElement.textContent = 'Setup Required';
      academicYearElement.classList.add('text-yellow-600');
    }
    if (termElement) {
      termElement.textContent = 'Setup Required';
      termElement.classList.add('text-yellow-600');
    }
    if (academicPeriodElement) {
      academicPeriodElement.textContent = 'Academic Setup Required';
      academicPeriodElement.classList.add('text-yellow-600');
    }
  } else {
    // Normal display when data exists
    if (academicYearElement) {
      academicYearElement.textContent = SR.currentAcademicYear;
      academicYearElement.classList.remove('text-yellow-600');
    }
    if (termElement) {
      termElement.textContent = SR.currentTerm;
      termElement.classList.remove('text-yellow-600');
    }
    if (academicPeriodElement) {
      academicPeriodElement.textContent = `${SR.currentAcademicYear} - ${SR.currentTerm}`;
      academicPeriodElement.classList.remove('text-yellow-600');
    }
  }

}


// Refresh academic information from server
async function refreshAcademicInfo() {
  try {
    // Get current academic context from API
    const context = await window.AcademicAPI.getCurrentContext();

    if (context && context.academicYear) {
      SR.currentAcademicYear = context.academicYear.name;
      SR.currentTerm = context.currentTerm ? context.currentTerm.name : null;

      console.log('✅ Academic info refreshed:', {
        year: SR.currentAcademicYear,
        term: SR.currentTerm
      });

      updateAcademicInfoDisplay();
    } else {
      console.log('📋 Academic context not yet available - may be still setting up');
      SR.currentAcademicYear = null;
      SR.currentTerm = null;
      updateAcademicInfoDisplay();
    }
  } catch (error) {
    console.error('Failed to refresh academic info:', error);

  }
}





// Load dashboard - now handled by modern navigation system
function loadDashboard() {
  console.log('📊 Loading dashboard...');

  // Use the modern page router to load dashboard
  if (window.PageRouter) {
    window.PageRouter.loadPage('dashboard');
  }
}

// Utility functions
function showError(message) {
  console.error('Error:', message);
  if (window.showNotification) {
    showNotification(message, 'error');
  } else {
    alert('Error: ' + message);
  }
}

function showSuccess(message) {
  if (window.showNotification) {
    showNotification(message, 'success');
  }
}

// Toggle user menu
function toggleUserMenu() {
  const dropdown = document.getElementById('user-dropdown');
  if (dropdown) {
    dropdown.classList.toggle('hidden');
  }
}

// Show profile settings modal
function showProfileModal() {
  if (window.Layout && window.Layout.showProfileModal) {
    window.Layout.showProfileModal();
  } else {
    console.error('Layout.showProfileModal not available');
  }
}

// Show logout confirmation modal
function logout() {
  console.log('🔄 Logout function called');

  if (window.Layout && window.Layout.showLogoutConfirmation) {
    console.log('✅ Showing logout confirmation modal');
    window.Layout.showLogoutConfirmation();
  } else {
    console.error('❌ Layout.showLogoutConfirmation not available');

    if (confirm('Are you sure you want to logout?')) {
      window.AuthManager.logout();
    }
  }
}

// Close user menu when clicking outside
document.addEventListener('click', function(event) {
  const userMenuButton = event.target.closest('button[onclick="toggleUserMenu()"]');
  const dropdown = document.getElementById('user-dropdown');

  if (dropdown && !dropdown.contains(event.target) && !userMenuButton) {
    dropdown.classList.add('hidden');
  }
});


// Toggle sidebar collapse (delegate to PageRouter)
function toggleSidebar() {
  if (window.PageRouter && window.PageRouter.toggleSidebar) {
    window.PageRouter.toggleSidebar();
  }
}



// Export functions for global access
window.toggleUserMenu = toggleUserMenu;
window.logout = logout; // This refers to the logout function defined at lines 570-580
window.refreshAcademicInfo = refreshAcademicInfo;
window.updateAcademicInfoDisplay = updateAcademicInfoDisplay;
window.toggleSidebar = toggleSidebar;
window.initializeApplication = initializeApplication;
window.loadDashboard = loadDashboard;
window.showLoginScreen = showLoginScreen;
window.restoreLoginScreen = restoreLoginScreen;
window.hideLoadingScreen = hideLoadingScreen;
window.updateLoadingText = updateLoadingText;




// Global utility functions
window.showError = showError;
window.showSuccess = showSuccess;
window.updateUserInterface = updateUserInterface;
window.verifyUIComponents = verifyUIComponents;



// closeModal function should be provided by ui-helpers.js



// Initialize modal container if it doesn't exist
function ensureModalContainer() {
  if (!document.getElementById('modal-container')) {
    const modalContainer = document.createElement('div');
    modalContainer.id = 'modal-container';
    modalContainer.className = 'fixed inset-0 z-50 hidden bg-black/50 backdrop-blur-sm';
    document.body.appendChild(modalContainer);
  }
}

// Initialize modern components
function initializeModernComponents() {
  ensureModalContainer();

  setTimeout(() => {
    checkComponentStatus();
  }, 100);
}

// Component status checker
function checkComponentStatus() {
  const components = {
    'Layout': window.Layout,
    'PageRouter': window.PageRouter,
    'Dashboard': window.Dashboard
  };

  const status = {};
  let allLoaded = true;

  for (const [name, component] of Object.entries(components)) {
    const isLoaded = component !== undefined;
    status[name] = isLoaded;

    if (!isLoaded) {
      allLoaded = false;
    }
  }

  return status;
}

// Export component functions
window.checkComponentStatus = checkComponentStatus;
